# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta, date
import warnings
from io import BytesIO
import traceback
import re
import json
import os
import platform
import matplotlib.font_manager as fm
from openai import OpenAI
from config import AI_CONFIG, AI_SYSTEM_PROMPT, ANALYSIS_PROMPT_TEMPLATE, ANOMALY_THRESHOLD, VISUALIZATION_CONFIG, OUTPUT_CONFIG, DATA_FETCH_CONFIG, CHANNEL_FILTER_CONFIG, get_current_ai_config
import random
import matplotlib.dates as mdates
import xlsxwriter
import glob
import concurrent.futures
import time
import ssl
from httpx import ConnectError, RemoteProtocolError, TimeoutException
from openai import APIConnectionError, APITimeoutError
import socket
import requests

warnings.filterwarnings('ignore')

plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("deep")
sns.set_context("notebook", font_scale=1.2)

def set_chinese_font():
    system_os = platform.system()
    font_paths_macos = [
        '/System/Library/Fonts/PingFang.ttc', '/Library/Fonts/PingFang.ttc',
        '/System/Library/Fonts/STHeiti Light.ttc', '/System/Library/Fonts/STHeiti Medium.ttc',
        '/Library/Fonts/Arial Unicode.ttf'
    ]
    font_paths_windows = [
        'C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/simhei.ttf',
        'C:/Windows/Fonts/simsun.ttc', 'C:/Windows/Fonts/Deng.ttf'
    ]
    font_paths_to_try = font_paths_macos + font_paths_windows if system_os == 'Darwin' else font_paths_windows + font_paths_macos
    for font_path in font_paths_to_try:
        if os.path.exists(font_path):
            try:
                font_prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.family'] = font_prop.get_name()
                plt.rcParams['axes.unicode_minus'] = False
                return True
            except Exception as e:
                pass
    try:
        fallback_fonts = ['Microsoft YaHei', 'SimHei', 'PingFang SC', 'Arial Unicode MS', 'sans-serif']
        plt.rcParams['font.sans-serif'] = fallback_fonts
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except Exception as e:
        pass
    plt.rcParams['axes.unicode_minus'] = False
    return False

set_chinese_font()

def convert_numpy_types(obj):
    import pandas as pd
    import numpy as np
    from datetime import datetime, date
    
    if isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif pd.isna(obj):
        return None
    elif isinstance(obj, (pd.Timestamp, datetime)):
        return obj.strftime('%Y-%m-%d') if obj else None
    elif isinstance(obj, date):
        return obj.strftime('%Y-%m-%d') if obj else None
    elif isinstance(obj, pd.Period):
        return str(obj) if obj else None
    elif isinstance(obj, (np.datetime64,)):
        return pd.to_datetime(obj).strftime('%Y-%m-%d') if pd.notna(obj) else None
    elif hasattr(obj, 'item'):
        item_val = obj.item()
        # 递归处理item()的结果
        return convert_numpy_types(item_val)
    elif hasattr(obj, 'dtype') and hasattr(obj, 'tolist'):
        return obj.tolist()
    elif isinstance(obj, (np.int8, np.int16, np.int32, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

def _convert_percentage_column(series):
    if series.empty:
        return series
    if series.dtype == 'object':
        try:
            numeric_series = pd.to_numeric(series.astype(str).str.rstrip('%'), errors='coerce') / 100.0
            return numeric_series.fillna(0)
        except Exception as e:
            try:
                numeric_series = pd.to_numeric(series.astype(str).str.replace('%', '', regex=False), errors='coerce') / 100.0
                return numeric_series.fillna(0)
            except Exception as backup_e:
                 print(f"错误: 列 '{series.name}' 备选转换也失败: {backup_e}")
                 return series
    elif pd.api.types.is_numeric_dtype(series):
        if (series > 1).any():
            if series.name and ('付费率' in str(series.name) or '付费率' == str(series.name)):
                return series / 100.0
            elif (series <= 100).mean() > 0.8:
                return series / 100.0
            else:
                return series
        else:
            return series
    else:
        try:
            numeric_series = pd.to_numeric(series, errors='coerce') / 100.0
            return numeric_series.fillna(0)
        except Exception as e:
            print(f"错误: 强制转换列 '{series.name}' 失败: {e}")
            return series

def _calculate_change_rate(current, previous):
    if previous is None or pd.isna(previous) or previous == 0:
        if current is not None and not pd.isna(current) and current != 0:
            return 9999.9
        else:
            return 0.0
    if current is None or pd.isna(current):
        return -100.0
    try:
        result = ((float(current) - float(previous)) / float(previous)) * 100.0
        if not np.isfinite(result):
            return 9999.9 if result > 0 else -9999.9
        return result
    except (ValueError, TypeError, ZeroDivisionError, OverflowError) as e:
        print(f"警告: 无法计算变化率，出错: {e}, 输入值: current={current}, previous={previous}")
        return 0.0

def _find_value_column(df, candidates, default_name='汇总值'):
    value_col = None
    original_value_col_name = None
    for col_candidate in candidates:
        if col_candidate in df.columns:
            value_col = col_candidate
            original_value_col_name = col_candidate
            break
    if not value_col:
        keywords = ['金额', '价格', '总和', '价值', '数值']
        potential_value_cols = [
            c for c in df.columns
            if any(keyword in c for keyword in keywords) and
               c not in ['时间', '日期', '养成线', '活动分类', '创角渠道名称', '渠道']
        ]
        if potential_value_cols:
            value_col = potential_value_cols[0]
            original_value_col_name = value_col
        else:
            print(f"错误: 未能找到合适的数值列。已尝试: {candidates} 及包含关键字的列。")
            print(f"可用列: {df.columns.tolist()}")
            return None, None
    if value_col != default_name:
        df.rename(columns={value_col: default_name}, inplace=True)
        return default_name, original_value_col_name
    else:
        return default_name, original_value_col_name

def _pivot_weekly_data(df, category_col, value_col, channel_name, date_col='时间', week_start=None, keep_weeks=5):
    if df is None or df.empty:
        return None
    if date_col not in df.columns:
        print(f"错误: 缺少日期列 '{date_col}'")
        return None
    if category_col not in df.columns:
        print(f"错误: 缺少分类列 '{category_col}'")
        return None
    if value_col not in df.columns:
        print(f"错误: 缺少数值列 '{value_col}'")
        return None
    try:
        if not pd.api.types.is_datetime64_any_dtype(df[date_col]):
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            df.dropna(subset=[date_col], inplace=True)
            if df.empty:
                print("错误: 日期转换后数据为空")
                return None
        if not pd.api.types.is_numeric_dtype(df[value_col]):
            df[value_col] = pd.to_numeric(df[value_col], errors='coerce').fillna(0)
        df[category_col] = df[category_col].fillna('未知').replace('(null)', '未知')
        if week_start is None:
            df['周'] = df[date_col].dt.date
        else:
            df['周'] = df[date_col].dt.to_period(week_start).apply(lambda p: p.start_time)
        pivot_table = pd.pivot_table(df, values=value_col, index='周', columns=category_col, aggfunc='sum', fill_value=0)
        pivot_table = pivot_table.reset_index()
        pivot_table = pivot_table.sort_values('周')
        if keep_weeks is not None and len(pivot_table) > keep_weeks:
            pivot_table = pivot_table.tail(keep_weeks)
        return pivot_table
    except Exception as e:
        print(f"执行 _pivot_weekly_data 时出错 ({channel_name}, {category_col}, {value_col}): {e}")
        print(traceback.format_exc())
        return None

def load_data(file_path):
    if not file_path or not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        return None
    file_basename = os.path.basename(file_path)
    try:
        is_rc_dist_file = "_RC_DIST_" in file_basename or "RC_DIST" in file_basename
        is_activity_file = "_ACTIVITY_" in file_basename or "ACTIVITY" in file_basename
        is_activity2_file = "_ACTIVITY2_" in file_basename or "ACTIVITY2" in file_basename
        is_cultivation_file = "_GROWTH_" in file_basename or "GROWTH" in file_basename or "_CULTIVATION_" in file_basename or "CULTIVATION" in file_basename
        is_average_file = "_AVERAGE_" in file_basename or "AVERAGE" in file_basename
        try:
            df = pd.read_csv(file_path, encoding='utf-8', on_bad_lines='warn')
        except TypeError:
            df = pd.read_csv(file_path, encoding='utf-8', error_bad_lines=False, warn_bad_lines=True)
        df.columns = [col.strip('"\'') if isinstance(col, str) else col for col in df.columns]
        if df.empty:
            print(f"错误: 文件为空 {file_path}")
            return None
        if is_rc_dist_file:
            return df
        if is_activity_file:
            return df
        if is_activity2_file:
            return df
        if is_cultivation_file:
            return df
        if is_average_file:
            return df
        date_col_name = None
        if '时间' in df.columns:
            date_col_name = '时间'
        elif '日期' in df.columns:
            date_col_name = '日期'
        else:
            print(f"错误: 文件 {file_basename} 中缺少 '时间' 或 '日期' 列")
            return None
        if df[date_col_name].dtype == 'object':
            summary_rows = df[df[date_col_name] == '阶段汇总']
            if not summary_rows.empty:
                df = df[df[date_col_name] != '阶段汇总'].copy()
        try:
            df[date_col_name] = pd.to_datetime(df[date_col_name], errors='coerce')
            original_len = len(df)
            df.dropna(subset=[date_col_name], inplace=True)
            if len(df) < original_len:
                pass
            if date_col_name != '日期':
                df = df.rename(columns={date_col_name: '日期'})
            if '流水' in df.columns and '日流水' not in df.columns:
                df = df.rename(columns={'流水': '日流水'})
        except Exception as date_err:
            print(f"错误: 日期列格式转换失败: {date_err}")
            return None
        channel_col_name = None
        channel_candidates = ['创角渠道名称', '渠道', '渠道名称']
        for candidate in channel_candidates:
            if candidate in df.columns:
                channel_col_name = candidate
                break
        target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
        is_all_mode = target_channel_config == "all"
        if not is_all_mode and not channel_col_name:
            print(f"错误: 文件 {file_basename} 在非 'all' 模式下缺少渠道列 (尝试过: {channel_candidates})")
            return None
        elif channel_col_name and channel_col_name != '创角渠道名称':
            df = df.rename(columns={channel_col_name: '创角渠道名称'})
        elif channel_col_name:
             df['创角渠道名称'] = df['创角渠道名称'].astype(str).fillna('未知渠道')
        is_new_file = '_NEW_' in file_basename
        if is_new_file:
            percentage_cols = ['付费率', '新增付费率']
            numeric_cols = ['ARPPU', '新增ARPPU']
        else:
            percentage_cols = ['付费率']
            numeric_cols = ['日流水', '日DAU', '付费DAU', '活跃付费DAU', 'ARPU', 'ARPPU',
                           '超R人数', '超R充值', '大R人数', '大R充值',
                           '中R人数', '中R充值', '小R人数', '小R充值',
                           '半年+老用户DAU', '一年+老用户DAU']
            if 'ARPU' not in df.columns and '日流水' in df.columns and '日DAU' in df.columns:
                 df['ARPU'] = df.apply(lambda row: row['日流水'] / row['日DAU'] if row['日DAU'] else 0, axis=1)
            if 'ARPPU' not in df.columns and '日流水' in df.columns and '付费DAU' in df.columns:
                 df['ARPPU'] = df.apply(lambda row: row['日流水'] / row['付费DAU'] if row['付费DAU'] else 0, axis=1)
        for col in percentage_cols:
            if col in df.columns:
                df[col] = _convert_percentage_column(df[col])
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        df = df.sort_values('日期')
        return df
    except FileNotFoundError:
        print(f"错误: 文件未找到 {file_path}")
        return None
    except pd.errors.EmptyDataError:
        print(f"错误: 文件为空 {file_path}")
        return None
    except Exception as e:
        print(f"加载或处理文件 {file_path} 时出错: {e}")
        print(traceback.format_exc())
        return None

def separate_data_by_channel(df):
    if df is None or df.empty:
        print("错误: 无法按渠道分离数据，输入为空")
        return {}
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        return {'全渠道汇总': df.copy()}
    if '创角渠道名称' not in df.columns:
        if not target_channel_config:
            return {'全渠道汇总': df.copy()}
        else:
            print(f"错误: 需要筛选渠道 '{target_channel_config}'，但缺少 '创角渠道名称' 列")
            return {}
    df['创角渠道名称'] = df['创角渠道名称'].astype(str).fillna('未知渠道')
    channels = df['创角渠道名称'].unique()
    channel_data = {}
    if not target_channel_config:
        for channel in channels:
            channel_df = df[df['创角渠道名称'] == channel].copy()
            channel_data[channel] = channel_df
        return channel_data
    for channel in channels:
        channel_df = df[df['创角渠道名称'] == channel].copy()
        channel_data[channel] = channel_df
    return channel_data

def aggregate_channel_data(df, version_name, user_type="OLD"):
    if df is None or df.empty:
        return None
    if '日期' not in df.columns or not pd.api.types.is_datetime64_any_dtype(df['日期']):
        print(f"错误: {version_name} ({user_type}) 数据缺少有效的 '日期' 列，无法聚合")
        return None
    column_mapping = {}
    if '流水' in df.columns and '日流水' not in df.columns:
        column_mapping['流水'] = '日流水'
    if column_mapping:
        df = df.rename(columns=column_mapping)
    sum_cols_old = [
        '日流水', '日DAU', '付费DAU', '活跃付费DAU', '新增', '新增付费', '新增付费金额',
        '超R人数', '超R充值', '大R人数', '大R充值',
        '中R人数', '中R充值', '小R人数', '小R充值',
        '半年+老用户DAU', '一年+老用户DAU'
    ]
    recalc_cols_old = ['付费率', 'ARPU', 'ARPPU', '新增付费率', '新增ARPPU']
    avg_cols_new = ['付费率', 'ARPPU', '新增付费率', '新增ARPPU']
    agg_strategy = {}
    for col in df.columns:
        if col != '日期' and col not in ['channel', '渠道', '游戏名称', '游戏ID', '创角渠道名称']:
            if pd.api.types.is_numeric_dtype(df[col]):
                if user_type == "OLD":
                    if col in recalc_cols_old:
                        agg_strategy[col] = 'mean'
                    else:
                        agg_strategy[col] = 'sum'
                else:
                    if col in avg_cols_new:
                        agg_strategy[col] = 'mean'
                    else:
                        agg_strategy[col] = 'sum'
            else:
                agg_strategy[col] = 'first'
    if not agg_strategy:
        return pd.DataFrame(columns=['日期']).set_index('日期')
    grouped = df.groupby('日期')
    agg_df = grouped.agg(agg_strategy)
    if user_type == "OLD":
        if '付费率' in recalc_cols_old and '付费DAU' in agg_df and '日DAU' in agg_df:
            agg_df['付费率'] = agg_df.apply(lambda row: row['付费DAU'] / row['日DAU'] if row['日DAU'] else 0, axis=1)
        if 'ARPU' in recalc_cols_old and '日流水' in agg_df and '日DAU' in agg_df:
            agg_df['ARPU'] = agg_df.apply(lambda row: row['日流水'] / row['日DAU'] if row['日DAU'] else 0, axis=1)
        if 'ARPPU' in recalc_cols_old and '日流水' in agg_df and '付费DAU' in agg_df:
            agg_df['ARPPU'] = agg_df.apply(lambda row: row['日流水'] / row['付费DAU'] if row['付费DAU'] else 0, axis=1)
        if '新增付费率' in recalc_cols_old and '新增付费' in agg_df and '新增' in agg_df:
            agg_df['新增付费率'] = agg_df.apply(lambda row: row['新增付费'] / row['新增'] if row['新增'] else 0, axis=1)
        if '新增ARPPU' in recalc_cols_old and '新增付费金额' in agg_df and '新增付费' in agg_df:
            agg_df['新增ARPPU'] = agg_df.apply(lambda row: row['新增付费金额'] / row['新增付费'] if row['新增付费'] else 0, axis=1)
    agg_df = agg_df.reset_index()
    agg_df['创角渠道名称'] = f"{version_name} 汇总"
    return agg_df

def _calculate_metric_comparison(series, n_periods=4):
    if series is None or series.empty or len(series) < 1:
        return {'current': None, 'previous': None, 'growth_rate': None}
    current_value = series.iloc[0] if len(series) >= 1 else None
    previous_values = series.iloc[1:n_periods+1] if len(series) > 1 else pd.Series(dtype=float)
    previous_mean = previous_values.mean() if not previous_values.empty else None
    growth_rate = _calculate_change_rate(current_value, previous_mean)
    return {
        'current': current_value,
        'previous': previous_mean,
        'growth_rate': growth_rate
    }

def _detect_anomalies(metrics_comparison, threshold=ANOMALY_THRESHOLD, original_data=None):
    anomalies = []
    latest_week_data = None
    if original_data is not None and not original_data.empty:
        latest_week_data = original_data.sort_values(by='日期', ascending=True).iloc[-1]
    for metric, comparison_data in metrics_comparison.items():
        growth_rate = comparison_data.get('growth_rate')
        if growth_rate is not None and pd.notna(growth_rate) and abs(growth_rate) >= threshold:
            current_value = None
            if latest_week_data is not None and metric in latest_week_data:
                current_value = latest_week_data[metric]
            else:
                current_value = comparison_data.get('current')
            anomalies.append({
                '指标': metric,
                '当期平均': current_value,
                '上周平均': comparison_data.get('previous'),
                '变化率(%)': growth_rate,
                '方向': '增长' if growth_rate > 0 else '下降'
            })
    return anomalies

def _calculate_tier_metrics_comparison(df, n_periods=4):
    tier_metrics_comparison = {}
    tier_metrics_config = [
        '小 R 人数', '小 R 充值', '中 R 人数', '中 R 充值',
        '大 R 人数', '大 R 充值', '超 R 人数', '超 R 充值'
    ]
    for metric in tier_metrics_config:
        if metric in df.columns:
            sorted_series = df.sort_values(by='日期', ascending=False)[metric]
            tier_metrics_comparison[metric] = _calculate_metric_comparison(sorted_series, n_periods)
    return tier_metrics_comparison

def analyze_channel_data(channel_df, channel_name, anomaly_threshold=ANOMALY_THRESHOLD, version=None):
    if not isinstance(channel_df, pd.DataFrame) or channel_df.empty:
        return None
    if '日期' not in channel_df.columns or not pd.api.types.is_datetime64_any_dtype(channel_df['日期']):
        print(f"错误: 渠道 '{channel_name}' 数据缺少有效的 '日期' 列")
        return None
    channel_df = channel_df.sort_values(by='日期', ascending=False).copy()
    percentage_columns = ['付费率', '新增付费率']
    numeric_candidates = ['新增', '流水', '付费 ARPU', 'ARPU', 'ARPPU', '新增ARPPU', '日DAU', '付费DAU',
                          '小 R 人数', '中 R 人数', '大 R 人数', '超 R 人数',
                          '小 R 充值', '中 R 充值', '大 R 充值', '超 R 充值']
    for col in percentage_columns:
        if col in channel_df.columns:
            channel_df[col] = _convert_percentage_column(channel_df[col])
    for col in numeric_candidates:
        if col in channel_df.columns:
            channel_df[col] = pd.to_numeric(channel_df[col], errors='coerce').fillna(0)
    if len(channel_df) < 2:
        pass
    metrics_total = {}
    analysis_columns = [col for col in channel_df.columns if col not in ['日期', '渠道', '游戏名称', '游戏ID', '创角渠道名称']]
    for col in analysis_columns:
        if pd.api.types.is_numeric_dtype(channel_df[col]):
            metrics_total[col] = _calculate_metric_comparison(channel_df[col], n_periods=4)
    anomalies = _detect_anomalies(metrics_total, anomaly_threshold, original_data=channel_df)
    last_week_data = channel_df.iloc[0:1] if not channel_df.empty else None
    previous_4_weeks_data = channel_df.iloc[1:5] if len(channel_df) > 1 else None
    last_week_start = last_week_data['日期'].iloc[0] if last_week_data is not None else None
    prev_4weeks_start = previous_4_weeks_data['日期'].min() if previous_4_weeks_data is not None and not previous_4_weeks_data.empty else None
    prev_4weeks_end = previous_4_weeks_data['日期'].max() if previous_4_weeks_data is not None and not previous_4_weeks_data.empty else None
    last_week_start_str = last_week_start.strftime('%Y-%m-%d') if pd.notna(last_week_start) else 'N/A'
    prev_weeks_start_str = prev_4weeks_start.strftime('%Y-%m-%d') if pd.notna(prev_4weeks_start) else 'N/A'
    prev_weeks_end_str = prev_4weeks_end.strftime('%Y-%m-%d') if pd.notna(prev_4weeks_end) else 'N/A'
    explicit_version = version
    if explicit_version is None:
        if channel_name.startswith('E-') or "E版本" in channel_name or "E 汇总" in channel_name:
            explicit_version = "E"
        elif channel_name.startswith('D-') or "D版本" in channel_name or "D 汇总" in channel_name:
            explicit_version = "D"
    api_averages = load_api_average_data(channel_name, explicit_version)
    if api_averages and 'weekly_data' in api_averages and api_averages['weekly_data']:
        latest_week_data = api_averages['weekly_data'][-1] if len(api_averages['weekly_data']) > 0 else None
        previous_weeks_data = api_averages['weekly_data'][:-1] if len(api_averages['weekly_data']) > 1 else []
        if latest_week_data and 'DAU' in latest_week_data:
            current_dau = latest_week_data['DAU']
            previous_dau_values = [week['DAU'] for week in previous_weeks_data if 'DAU' in week and pd.notna(week['DAU'])]
            previous_dau = sum(previous_dau_values) / len(previous_dau_values) if previous_dau_values else 0
            dau_change = _calculate_change_rate(current_dau, previous_dau)
            metrics_total['DAU'] = {
                'current': current_dau,
                'previous': previous_dau,
                'growth_rate': dau_change
            }
        if latest_week_data and '付费率' in latest_week_data:
            current_rate = latest_week_data['付费率']
            previous_rate_values = [week['付费率'] for week in previous_weeks_data if '付费率' in week and pd.notna(week['付费率'])]
            previous_rate = sum(previous_rate_values) / len(previous_rate_values) if previous_rate_values else 0
            if current_rate > 1:
                current_rate /= 100.0
            if previous_rate > 1:
                previous_rate /= 100.0
            rate_change = _calculate_change_rate(current_rate, previous_rate)
            metrics_total['付费率'] = {
                'current': current_rate,
                'previous': previous_rate,
                'growth_rate': rate_change
            }
    analysis_result = {
        'channel': channel_name,
        'metrics_total': metrics_total,
        'anomalies': anomalies,
        'last_7_days': {'start_date': last_week_start_str, 'end_date': '本周'},
        'previous_7_days': {'start_date': prev_weeks_start_str, 'end_date': prev_weeks_end_str},
        'original_data': channel_df.sort_values(by='日期', ascending=True),
        'api_averages': api_averages,
        'all_metrics_comparison': metrics_total
    }
    return analysis_result

def analyze_activity_data(activity_df, channel_name):
    if activity_df is None or activity_df.empty:
        return None
    df = activity_df.copy()
    date_col = None
    if '时间' in df.columns: date_col = '时间'
    elif '日期' in df.columns: date_col = '日期'
    if not date_col:
        print(f"错误: 活动数据缺少日期/时间列。可用列: {df.columns.tolist()}")
        return None
    if date_col != '时间': df.rename(columns={date_col: '时间'}, inplace=True)
    category_col = None
    category_candidates = ['活动分类', '分类', '类别']
    for candidate in category_candidates:
        if candidate in df.columns:
            category_col = candidate
            break
    if not category_col:
        print(f"错误: 活动数据缺少分类列 (尝试过: {category_candidates})。可用列: {df.columns.tolist()}")
        return None
    if category_col != '活动分类': df.rename(columns={category_col: '活动分类'}, inplace=True)
    channel_col = None
    channel_candidates = ['创角渠道名称', '渠道']
    for candidate in channel_candidates:
        if candidate in df.columns:
            channel_col = candidate
            break
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        if not channel_col:
            df['创角渠道名称'] = '全渠道汇总'
        elif channel_col != '创角渠道名称':
            df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    elif not channel_col:
        if not target_channel_config:
            df['创角渠道名称'] = '全渠道汇总'
        else:
            print(f"错误: 活动数据缺少渠道列 (尝试过: {channel_candidates})。可用列: {df.columns.tolist()}")
            return None
    elif channel_col != '创角渠道名称':
        df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    value_col_candidates = ['充值金额', '充值日志.价格总和', '价格总和', '金额']
    value_col, _ = _find_value_column(df, value_col_candidates, default_name='充值金额')
    if not value_col: return None
    if '汇总' not in channel_name:
        real_channel_name = channel_name.replace('D-', '').replace('E-', '')
        df = df[df['创角渠道名称'] == real_channel_name]
        if df.empty:
            return None
    pivot_table = _pivot_weekly_data(df,
                                     category_col='活动分类',
                                     value_col=value_col,
                                     channel_name=channel_name,
                                     date_col='时间',
                                     week_start=None,
                                     keep_weeks=5)
    if pivot_table is None:
        return None
    activity_columns = [col for col in pivot_table.columns if col != '周']
    activity_columns.sort()
    final_columns = ['周'] + activity_columns
    pivot_table = pivot_table[final_columns]
    return pivot_table

def analyze_activity2_data(activity2_df, channel_name):
    if activity2_df is None or activity2_df.empty:
        return None
    df = activity2_df.copy()
    date_col = None
    if '时间' in df.columns: date_col = '时间'
    elif '日期' in df.columns: date_col = '日期'
    if not date_col:
        print(f"错误: 活动2数据缺少日期/时间列。可用列: {df.columns.tolist()}")
        return None
    if date_col != '时间': df.rename(columns={date_col: '时间'}, inplace=True)
    activity_col = None
    if '活动名称' in df.columns: activity_col = '活动名称'
    elif 'activities_name' in df.columns: activity_col = 'activities_name'
    elif 'activity_name' in df.columns: activity_col = 'activity_name'
    if not activity_col:
        print(f"错误: 活动2数据缺少活动名称列。可用列: {df.columns.tolist()}")
        return None
    if activity_col != '活动名称': df.rename(columns={activity_col: '活动名称'}, inplace=True)
    value_col_candidates = ['充值金额', '充值日志.价格总和', '价格总和', '金额']
    value_col, _ = _find_value_column(df, value_col_candidates, default_name='充值金额')
    if not value_col: return None
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        pass
    elif '汇总' not in channel_name:
        real_channel_name = channel_name.replace('D-', '').replace('E-', '')
        channel_col = None
        if '创角渠道名称' in df.columns: channel_col = '创角渠道名称'
        elif 'create_package_id@channel' in df.columns: channel_col = 'create_package_id@channel'
        elif 'cre_package_id@channel' in df.columns: channel_col = 'cre_package_id@channel'
        if channel_col:
            df = df[df[channel_col] == real_channel_name]
            if df.empty:
                return None
        else:
            if not target_channel_config:
                pass
            else:
                return None
    pivot_table = _pivot_weekly_data(df,
                                     category_col='活动名称',
                                     value_col=value_col,
                                     channel_name=channel_name,
                                     date_col='时间',
                                     week_start=None,
                                     keep_weeks=5)
    if pivot_table is None:
        return None
    return pivot_table

def analyze_top20_data(top20_df, channel_name):
    if top20_df is None or top20_df.empty:
        return None
    df = top20_df.copy()
    time_col = None
    if '时间' in df.columns: time_col = '时间'
    elif '日期' in df.columns: time_col = '日期'
    role_id_col = None
    if '角色ID' in df.columns: role_id_col = '角色ID'
    elif 'role_id' in df.columns: role_id_col = 'role_id'
    if not role_id_col:
        print(f"错误: TOP20数据缺少角色ID列。可用列: {df.columns.tolist()}")
        return None
    if role_id_col != '角色ID': df.rename(columns={role_id_col: '角色ID'}, inplace=True)
    role_name_col = None
    if '角色名' in df.columns: role_name_col = '角色名'
    elif '角色名称' in df.columns: role_name_col = '角色名称'
    elif 'role_name' in df.columns: role_name_col = 'role_name'
    if not role_name_col:
        print(f"错误: TOP20数据缺少角色名称列。可用列: {df.columns.tolist()}")
        return None
    if role_name_col != '角色名': df.rename(columns={role_name_col: '角色名'}, inplace=True)
    total_amount_col = None
    if '累计充值金额' in df.columns: total_amount_col = '累计充值金额'
    elif '累计金额' in df.columns: total_amount_col = '累计金额'
    elif '累计充值金额@' in df.columns: total_amount_col = '累计充值金额@'
    elif 'total_recharge_amount' in df.columns: total_amount_col = 'total_recharge_amount'
    elif 'total_pay_amount' in df.columns: total_amount_col = 'total_pay_amount'
    if not total_amount_col:
        potential_cols = [col for col in df.columns if '累计' in col]
        if potential_cols:
            total_amount_col = potential_cols[0]
        else:
            print(f"错误: TOP20数据缺少累计充值金额列。可用列: {df.columns.tolist()}")
            return None
    if total_amount_col != '累计充值金额': df.rename(columns={total_amount_col: '累计充值金额'}, inplace=True)
    channel_col = None
    if '创角渠道名称' in df.columns: channel_col = '创角渠道名称'
    elif '创角渠道' in df.columns: channel_col = '创角渠道'
    elif 'create_package_id@channel' in df.columns: channel_col = 'create_package_id@channel'
    elif 'cre_package_id@channel' in df.columns: channel_col = 'cre_package_id@channel'
    if not channel_col:
        potential_cols = [col for col in df.columns if '渠道' in col]
        if potential_cols:
            channel_col = potential_cols[0]
        else:
            pass
    if channel_col and channel_col != '创角渠道名称':
        df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        pass
    elif '汇总' not in channel_name:
        real_channel_name = channel_name.replace('D-', '').replace('E-', '')
        if channel_col:
            df = df[df['创角渠道名称'] == real_channel_name]
            if df.empty:
                return None
        elif target_channel_config:
            print(f"错误: 无法在TOP20数据中筛选渠道 {real_channel_name}，因为缺少渠道列")
            return None
        else:
            pass
    date_columns = [col for col in df.columns if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日', str(col))]
    recharge_col = None
    recharge_candidates = ['充值日志.价格总和', '充值金额', '金额', '价格']
    for candidate in recharge_candidates:
        if candidate in df.columns:
            recharge_col = candidate
            break
    if not recharge_col:
        print(f"错误: TOP20数据缺少充值金额列 (尝试过: {recharge_candidates})。可用列: {df.columns.tolist()}")
        return None
    if time_col:
        try:
            df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
            df[recharge_col] = pd.to_numeric(df[recharge_col], errors='coerce').fillna(0)
            if isinstance(df[time_col].iloc[0], str):
                df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
            df['date_str'] = df[time_col].dt.strftime('%Y-%m-%d')
            pivot_df = df.pivot_table(
                index=['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额'],
                columns='date_str',
                values=recharge_col,
                aggfunc='sum',
                fill_value=0
            ).reset_index()
            pivot_df.columns.name = None
            date_cols = [col for col in pivot_df.columns if col not in ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额']]
            if date_cols:
                # 添加阶段汇总列
                pivot_df['阶段汇总'] = pivot_df[date_cols].sum(axis=1)
                cols = ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额', '阶段汇总'] + date_cols
                pivot_df = pivot_df[cols]
            df = pivot_df
            date_columns = [col for col in df.columns if col not in ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额', '阶段汇总']]
        except Exception as e:
            if not date_columns and time_col:
                try:
                    df['日期'] = df[time_col].dt.strftime('%Y-%m-%d')
                    date_columns = ['日期']
                except Exception as e:
                    pass
    sorted_date_columns = []
    if date_columns:
        try:
            date_objects = []
            for col in date_columns:
                try:
                    date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(col))
                    if date_match:
                        date_str = date_match.group(1)
                        date_obj = pd.to_datetime(date_str, errors='coerce')
                        if not pd.isna(date_obj):
                            date_objects.append((col, date_obj))
                    else:
                        date_obj = pd.to_datetime(col, errors='coerce')
                        if not pd.isna(date_obj):
                            date_objects.append((col, date_obj))
                except Exception as e:
                    pass
            date_objects.sort(key=lambda x: x[1])
            sorted_date_columns = [item[0] for item in date_objects]
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        except Exception as e:
            sorted_date_columns = date_columns
    else:
        pass
    if '角色ID' in df.columns:
        try:
            df['累计充值金额'] = pd.to_numeric(df['累计充值金额'], errors='coerce').fillna(0)
            df = df.sort_values('累计充值金额', ascending=False).drop_duplicates('角色ID').reset_index(drop=True)
        except Exception as e:
            pass
    
    # 修改排序逻辑：优先按阶段汇总排序，如果没有阶段汇总列则按累计充值金额排序
    if '阶段汇总' in df.columns:
        try:
            df['阶段汇总'] = pd.to_numeric(df['阶段汇总'], errors='coerce').fillna(0)
            df = df.sort_values(by='阶段汇总', ascending=False).reset_index(drop=True)
        except Exception as e:
            print(f"按阶段汇总排序失败，回退到按累计充值金额排序: {e}")
            df = df.sort_values(by='累计充值金额', ascending=False).reset_index(drop=True)
    else:
        # 回退到原来的排序方式
        df = df.sort_values(by='累计充值金额', ascending=False).reset_index(drop=True)
    if len(df) > 30:
        df = df.head(30)
    
    # 打印按阶段汇总排序的TOP30用户信息
    if '阶段汇总' in df.columns and not df.empty:
        print(f"\n=== {channel_name} 按阶段汇总排序的TOP30用户 ===")
        print("排名\t角色名\t\t角色ID\t\t阶段汇总\t累计充值金额")
        print("-" * 80)
        for idx, row in df.head(30).iterrows():
            role_name = str(row.get('角色名', '')).ljust(12)[:12]
            role_id = str(row.get('角色ID', ''))
            stage_total = int(row.get('阶段汇总', 0))
            total_amount = int(row.get('累计充值金额', 0))
            print(f"{idx+1:2d}\t{role_name}\t{role_id}\t{stage_total:8d}\t{total_amount:8d}")
        print("=" * 80)
    
    df.attrs['sorted_date_columns'] = sorted_date_columns
    return df

def analyze_cultivation_data(cultivation_df, channel_name):
    if cultivation_df is None or cultivation_df.empty:
        return None
    df = cultivation_df.copy()
    date_col = None
    if '时间' in df.columns: date_col = '时间'
    elif '日期' in df.columns: date_col = '日期'
    if not date_col:
        print(f"错误: 养成线数据缺少日期/时间列。可用列: {df.columns.tolist()}")
        return None
    if date_col != '时间': df.rename(columns={date_col: '时间'}, inplace=True)
    category_col = None
    category_candidates = ['养成线', '分类', '类别', '名称']
    for candidate in category_candidates:
        if candidate in df.columns:
            category_col = candidate
            break
    if not category_col:
        print(f"错误: 养成线数据缺少分类列 (尝试过: {category_candidates})。可用列: {df.columns.tolist()}")
        return None
    if category_col != '养成线': df.rename(columns={category_col: '养成线'}, inplace=True)
    channel_col = None
    channel_candidates = ['创角渠道名称', '渠道', '渠道名']
    for candidate in channel_candidates:
        if candidate in df.columns:
            channel_col = candidate
            break
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        if not channel_col:
            df['创角渠道名称'] = '全渠道汇总'
        elif channel_col != '创角渠道名称':
            df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    elif not channel_col:
        if not target_channel_config:
            df['创角渠道名称'] = '全渠道汇总'
        else:
            print(f"错误: 养成线数据缺少渠道列 (尝试过: {channel_candidates})。可用列: {df.columns.tolist()}")
            return None
    elif channel_col != '创角渠道名称':
        df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    value_col_candidates = ['充值日志.价格总和', '金额', '充值金额', '价值', '数值', '总和']
    value_col, _ = _find_value_column(df, value_col_candidates, default_name='汇总值')
    if not value_col: return None
    if '汇总' not in channel_name:
        real_channel_name = channel_name.replace('D-', '').replace('E-', '')
        df = df[df['创角渠道名称'] == real_channel_name]
        if df.empty:
            return None
    pivot_table = _pivot_weekly_data(df,
                                     category_col='养成线',
                                     value_col=value_col,
                                     channel_name=channel_name,
                                     date_col='时间',
                                     week_start=None,
                                     keep_weeks=5)
    if pivot_table is None:
        return None
    return pivot_table

def analyze_new_top_data(new_top_df, channel_name):
    if new_top_df is None or new_top_df.empty:
        return None
    df = new_top_df.copy()
    time_col = None
    if '时间' in df.columns: time_col = '时间'
    elif '日期' in df.columns: time_col = '日期'
    role_id_col = None
    if '角色ID' in df.columns: role_id_col = '角色ID'
    elif 'role_id' in df.columns: role_id_col = 'role_id'
    if not role_id_col:
        print(f"错误: 新注册用户TOP数据缺少角色ID列。可用列: {df.columns.tolist()}")
        return None
    if role_id_col != '角色ID': df.rename(columns={role_id_col: '角色ID'}, inplace=True)
    role_name_col = None
    if '角色名' in df.columns: role_name_col = '角色名'
    elif '角色名称' in df.columns: role_name_col = '角色名称'
    elif 'role_name' in df.columns: role_name_col = 'role_name'
    if not role_name_col:
        print(f"错误: 新注册用户TOP数据缺少角色名称列。可用列: {df.columns.tolist()}")
        return None
    if role_name_col != '角色名': df.rename(columns={role_name_col: '角色名'}, inplace=True)
    total_amount_col = None
    if '累计充值金额' in df.columns: total_amount_col = '累计充值金额'
    elif '累计金额' in df.columns: total_amount_col = '累计金额'
    elif '累计充值金额@' in df.columns: total_amount_col = '累计充值金额@'
    elif 'total_recharge_amount' in df.columns: total_amount_col = 'total_recharge_amount'
    elif 'total_pay_amount' in df.columns: total_amount_col = 'total_pay_amount'
    if not total_amount_col:
        potential_cols = [col for col in df.columns if '累计' in col]
        if potential_cols:
            total_amount_col = potential_cols[0]
        else:
            print(f"错误: 新注册用户TOP数据缺少累计充值金额列。可用列: {df.columns.tolist()}")
            return None
    if total_amount_col != '累计充值金额': df.rename(columns={total_amount_col: '累计充值金额'}, inplace=True)
    channel_col = None
    if '创角渠道名称' in df.columns: channel_col = '创角渠道名称'
    elif '创角渠道' in df.columns: channel_col = '创角渠道'
    elif 'create_package_id@channel' in df.columns: channel_col = 'create_package_id@channel'
    elif 'cre_package_id@channel' in df.columns: channel_col = 'cre_package_id@channel'
    if not channel_col:
        potential_cols = [col for col in df.columns if '渠道' in col]
        if potential_cols:
            channel_col = potential_cols[0]
        else:
            pass
    if channel_col and channel_col != '创角渠道名称':
        df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        pass
    elif '汇总' not in channel_name:
        real_channel_name = channel_name.replace('D-', '').replace('E-', '')
        if channel_col:
            df = df[df['创角渠道名称'] == real_channel_name]
            if df.empty:
                return None
        elif target_channel_config:
            print(f"错误: 无法在新注册用户TOP数据中筛选渠道 {real_channel_name}，因为缺少渠道列")
            return None
        else:
            pass
    date_columns = [col for col in df.columns if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日', str(col))]
    recharge_col = None
    recharge_candidates = ['充值日志.价格总和', '充值金额', '金额', '价格']
    for candidate in recharge_candidates:
        if candidate in df.columns:
            recharge_col = candidate
            break
    if not recharge_col:
        print(f"错误: 新注册用户TOP数据缺少充值金额列 (尝试过: {recharge_candidates})。可用列: {df.columns.tolist()}")
        return None
    if time_col:
        try:
            df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
            df[recharge_col] = pd.to_numeric(df[recharge_col], errors='coerce').fillna(0)
            if isinstance(df[time_col].iloc[0], str):
                df[time_col] = pd.to_datetime(df[time_col], errors='coerce')
            df['date_str'] = df[time_col].dt.strftime('%Y-%m-%d')
            pivot_df = df.pivot_table(
                index=['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额'],
                columns='date_str',
                values=recharge_col,
                aggfunc='sum',
                fill_value=0
            ).reset_index()
            pivot_df.columns.name = None
            date_cols = [col for col in pivot_df.columns if col not in ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额']]
            if date_cols:
                # 添加阶段汇总列
                pivot_df['阶段汇总'] = pivot_df[date_cols].sum(axis=1)
                cols = ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额', '阶段汇总'] + date_cols
                pivot_df = pivot_df[cols]
            df = pivot_df
            date_columns = [col for col in df.columns if col not in ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额', '阶段汇总']]
        except Exception as e:
            if not date_columns and time_col:
                try:
                    df['日期'] = df[time_col].dt.strftime('%Y-%m-%d')
                    date_columns = ['日期']
                except Exception as e:
                    pass
    sorted_date_columns = []
    if date_columns:
        try:
            date_objects = []
            for col in date_columns:
                try:
                    date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(col))
                    if date_match:
                        date_str = date_match.group(1)
                        date_obj = pd.to_datetime(date_str, errors='coerce')
                        if not pd.isna(date_obj):
                            date_objects.append((col, date_obj))
                    else:
                        date_obj = pd.to_datetime(col, errors='coerce')
                        if not pd.isna(date_obj):
                            date_objects.append((col, date_obj))
                except Exception as e:
                    pass
            date_objects.sort(key=lambda x: x[1])
            sorted_date_columns = [item[0] for item in date_objects]
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        except Exception as e:
            sorted_date_columns = date_columns
    else:
        pass
    if '角色ID' in df.columns:
        try:
            df['累计充值金额'] = pd.to_numeric(df['累计充值金额'], errors='coerce').fillna(0)
            df = df.sort_values('累计充值金额', ascending=False).drop_duplicates('角色ID').reset_index(drop=True)
        except Exception as e:
            pass
    
    # 修改排序逻辑：优先按阶段汇总排序，如果没有阶段汇总列则按累计充值金额排序
    if '阶段汇总' in df.columns:
        try:
            df['阶段汇总'] = pd.to_numeric(df['阶段汇总'], errors='coerce').fillna(0)
            df = df.sort_values(by='阶段汇总', ascending=False).reset_index(drop=True)
        except Exception as e:
            print(f"按阶段汇总排序失败，回退到按累计充值金额排序: {e}")
            df = df.sort_values(by='累计充值金额', ascending=False).reset_index(drop=True)
    else:
        # 回退到原来的排序方式
        df = df.sort_values(by='累计充值金额', ascending=False).reset_index(drop=True)
    if len(df) > 10:
        df = df.head(10)
    df.attrs['sorted_date_columns'] = sorted_date_columns
    return df

def analyze_recharge_distribution(rc_dist_df, channel_name):
    if rc_dist_df is None or rc_dist_df.empty:
        return None
    df = rc_dist_df.copy()
    date_col = None
    date_candidates = ['日期', '时间', '日志时间', '"日期"', '\ufeff"日期']
    for candidate in date_candidates:
        if candidate in df.columns:
            date_col = candidate
            break
    if not date_col:
        print(f"错误: 充值分布数据缺少日期列 (尝试过: {date_candidates})。可用列: {df.columns.tolist()}")
        return None
    if date_col != '日期': df.rename(columns={date_col: '日期'}, inplace=True)
    channel_col = None
    channel_candidates = ['创角渠道名称', '渠道', '渠道名称', '创角渠道', '产品渠道', '"创角渠道名称"']
    for candidate in channel_candidates:
        if candidate in df.columns:
            channel_col = candidate
            break
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    if target_channel_config == "all":
        if not channel_col:
            df['创角渠道名称'] = '全渠道汇总'
        elif channel_col != '创角渠道名称':
            df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    elif not channel_col:
        if not target_channel_config:
            df['创角渠道名称'] = '全渠道汇总'
        else:
            print(f"错误: 充值分布数据缺少渠道列 (尝试过: {channel_candidates})。可用列: {df.columns.tolist()}")
            return None
    elif channel_col != '创角渠道名称':
        df.rename(columns={channel_col: '创角渠道名称'}, inplace=True)
    total_col = None
    total_candidates = ['_total_总和', 'total', '总和', '总计', '"_total_总和"', '全部用户']
    for candidate in total_candidates:
        if candidate in df.columns:
            total_col = candidate
            break
    if df['日期'].dtype == 'object' and df['日期'].str.contains('当周', na=False).any():
        df['日期'] = df['日期'].str.extract(r'(\d{4}-\d{2}-\d{2})', expand=False)
    df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
    df.dropna(subset=['日期'], inplace=True)
    if df.empty:
        print("错误: 日期转换后充值分布数据为空")
        return None
    interval_columns = []
    potential_interval_patterns = [
        r'^[\[\(]-?\d+(\.\d+)?\s*,\s*(\+?inf|\+?∞|-?\d+(\.\d+)?)[\)\]]$',
        r'_interval_total_总和$'
    ]
    for col in df.columns:
        if col == total_col or col in ['日期', '创角渠道名称']: continue
        is_interval = False
        for pattern in potential_interval_patterns:
            if re.match(pattern, col, re.IGNORECASE):
                interval_columns.append(col)
                is_interval = True
                break
        if not is_interval and (re.search(r'\d', col) and (re.search(r'[\(\)\[\],]', col))):
             interval_columns.append(col)
    if not interval_columns:
        print(f"错误: 未能自动识别充值金额区间列。可用列: {df.columns.tolist()}")
        return None
    def safe_to_float(value):
        if pd.isna(value): return 0.0
        if isinstance(value, (int, float)): return float(value)
        if isinstance(value, str):
            value = value.replace(',', '').strip()
            if '%' in value:
                try: return float(value.strip('%')) / 100.0
                except ValueError: return 0.0
            try: return float(value)
            except ValueError: return 0.0
        return 0.0
    for col in interval_columns:
        df[col] = df[col].apply(safe_to_float)
    if not total_col:
        total_col = '_total_总和'
        df[total_col] = df[interval_columns].sum(axis=1)
    else:
        df[total_col] = df[total_col].apply(safe_to_float)
    df['周'] = df['日期'].dt.date
    grouped = df.groupby(['周', '创角渠道名称'])
    result_data = []
    for (week, channel), group in grouped:
        total_users = group[total_col].iloc[0] if not group.empty else 0
        if total_users > 0:
            price_col = None
            if '充值日志.价格总和' in group.columns:
                price_col = '充值日志.价格总和'
            elif '充值日志.充值金额总和' in group.columns:
                price_col = '充值日志.充值金额总和'
            else:
                for col in group.columns:
                    if '充值' in col and '总和' in col:
                        price_col = col
                        break
            if price_col is None:
                price_col = '充值日志.价格总和'
            for interval_col_name in interval_columns:
                user_rows = group[group[price_col] == '用户数']
                interval_value = user_rows[interval_col_name].iloc[0] if not user_rows.empty else 0
                percentage = interval_value / total_users if total_users > 0 else 0
                price_rows = pd.DataFrame()
                possible_values = [price_col, '充值日志.价格总和', '充值日志.充值金额总和', '充值金额', '价格总和']
                for value in possible_values:
                    try:
                        temp_rows = group[group[price_col] == value]
                        if not temp_rows.empty:
                            price_rows = temp_rows
                            break
                    except Exception as e:
                        continue
                price_value = price_rows[interval_col_name].iloc[0] if not price_rows.empty and interval_col_name in price_rows.columns else 0
                if isinstance(price_value, str):
                    if price_value.strip() == '-':
                        price_value = 0
                    else:
                        try:
                            price_value = float(price_value)
                        except (ValueError, TypeError):
                            price_value = 0
                interval_name = interval_col_name
                lower, upper = 0, float('inf')
                match = re.search(r'[\[\(](-?\d+\.?\d*)\s*,\s*(\+?inf|\+?∞|-?\d+\.?\d*)[\)\]]', interval_name)
                if match:
                    try:
                        lower = float(match.group(1))
                        upper_str = match.group(2).lower()
                        if 'inf' in upper_str or '∞' in upper_str:
                            upper = float('inf')
                        else:
                            upper = float(upper_str)
                    except ValueError:
                        pass
                result_data.append({
                    '周': week,
                    '渠道': channel,
                    '区间': interval_name,
                    '下限': lower,
                    '上限': upper,
                    '用户数': interval_value,
                    '占比': percentage,
                    '充值日志.价格总和': price_value
                })
    if not result_data:
        return None
    distribution_df = pd.DataFrame(result_data)
    distribution_df = distribution_df.sort_values(['周', '渠道', '下限'])
    latest_weeks = sorted(distribution_df['周'].unique())[-5:]
    recent_distribution = distribution_df[distribution_df['周'].isin(latest_weeks)]
    return {
        'all_data': recent_distribution,
        'latest_data': recent_distribution[recent_distribution['周'] == latest_weeks[-1]] if latest_weeks else pd.DataFrame(),
        'latest_week': latest_weeks[-1] if latest_weeks else None
    }

def analyze_with_ai(channel_name, metrics_total, anomalies, last_7_days, previous_7_days, new_user_data=None, recharge_distribution=None, activity_analysis=None, activity2_analysis=None, cultivation_analysis=None, top20_analysis=None, new_top_analysis=None, raw_data_context=None):
    import time
    import ssl
    import socket
    import requests
    from httpx import ConnectError, RemoteProtocolError, TimeoutException
    from openai import APIConnectionError, APITimeoutError
    
    def test_network_connectivity():
        """测试网络连接性"""
        try:
            # 测试基本网络连接
            socket.create_connection(("*******", 53), timeout=5)
            print("✅ 基本网络连接正常")
            
            # 测试HTTPS连接
            response = requests.get("https://www.google.com", timeout=10)
            if response.status_code == 200:
                print("✅ HTTPS连接正常")
            
            # 测试目标API服务器连接
            api_host = current_config["BASE_URL"].replace("https://", "").replace("http://", "").split("/")[0]
            try:
                socket.create_connection((api_host, 443), timeout=10)
                print(f"✅ 目标API服务器 {api_host} 可达")
            except Exception as e:
                print(f"❌ 目标API服务器 {api_host} 连接失败: {e}")
                
        except Exception as e:
            print(f"❌ 网络连接测试失败: {e}")
    
    # 获取当前AI配置
    current_config = get_current_ai_config()
    
    # 从配置获取连接参数
    conn_config = current_config.get("CONNECTION_CONFIG", {})
    MAX_RETRIES = conn_config.get("MAX_RETRIES", 3)
    RETRY_DELAY = conn_config.get("RETRY_DELAY", 2)
    TIMEOUT_SECONDS = conn_config.get("TIMEOUT_SECONDS", 120)
    USE_EXPONENTIAL_BACKOFF = conn_config.get("EXPONENTIAL_BACKOFF", True)
    
    try:
        if not current_config.get("API_KEY") or current_config["API_KEY"] == "your_openai_api_key_here":
            return "AI分析跳过：API Key未配置。"
        
        provider = AI_CONFIG.get("API_PROVIDER", "openrouter")
        print(f"🔧 使用AI提供商: {provider}")
        print(f"🔧 当前模型: {current_config['MODEL']}")
        print(f"🔧 连接配置: 超时{TIMEOUT_SECONDS}秒, 最大重试{MAX_RETRIES}次, 初始延迟{RETRY_DELAY}秒")
        
        # 创建客户端时添加更多配置
        try:
            client = OpenAI(
                api_key=current_config["API_KEY"],
                base_url=current_config["BASE_URL"],
                timeout=TIMEOUT_SECONDS,
                max_retries=0  # 我们自己处理重试逻辑
            )
        except Exception as e:
            print(f"❌ OpenAI客户端初始化失败: {e}")
            return generate_fallback_analysis(channel_name, metrics_total, anomalies)
        
        # 数据准备部分保持不变
        api_version = "d"
        if "oppo" in channel_name.lower():
            api_version = "d"
        elif "E版本" in channel_name or "E 汇总" in channel_name:
            api_version = "e"
        else:
            api_version = "d"
        api_file_path = f"api/{api_version}_data.txt"
        try:
            if os.path.exists(api_file_path):
                with open(api_file_path, 'r', encoding='utf-8') as f:
                    api_data = json.load(f)
            else:
                api_data = None
        except Exception as e:
            api_data = None
        current_date_range = f"{last_7_days.get('start_date', '未知')} (当前周)"
        previous_date_range = f"{previous_7_days.get('start_date', '未知')} 至 {previous_7_days.get('end_date', '未知')} (过去4周均值)"
        formatted_anomalies = []
        for anomaly in anomalies:
            metric = anomaly.get('指标', '')
            current = anomaly.get('当期平均', 0)
            previous = anomaly.get('上周平均', 0)
            change = anomaly.get('变化率(%)', 0)
            direction = "增长" if change >= 0 else "下降"
            current_fmt = f"{current:.2%}" if '率' in metric else f"{current:.2f}"
            prev_fmt = f"{previous:.2%}" if '率' in metric else f"{previous:.2f}"
            formatted_anomalies.append(f"{metric}: 当前周 {current_fmt}, 过去4周均值 {prev_fmt}, {direction}{abs(change):.2f}%")

        # ================================
        # 增强数据传递：确保AI获得全量数据
        # ================================
        
        # 1. 完整的核心指标数据（保持原有逻辑但增加更多细节）
        all_metrics = {}
        for metric_key, metric_data in metrics_total.items():
            current_val = metric_data.get('current', 0)
            prev_val = metric_data.get('previous', 0)
            change_val = metric_data.get('growth_rate', 0)
            is_rate = '率' in metric_key
            is_count = 'DAU' in metric_key or '新增' in metric_key
            
            if pd.isna(current_val) or current_val is None:
                current_val = 0
            if pd.isna(prev_val) or prev_val is None:
                prev_val = 0
            if pd.isna(change_val) or change_val is None:
                change_val = 0
            
            # 格式化显示值
            if is_rate and current_val > 0 and current_val < 1:
                current_val_str = f"{current_val:.2%}"
                prev_val_str = f"{prev_val:.2%}"
            elif is_count:
                current_val_str = f"{current_val:,.0f}"
                prev_val_str = f"{prev_val:,.0f}"
            else:
                current_val_str = f"{current_val:.2f}"
                prev_val_str = f"{prev_val:.2f}"
            
            all_metrics[metric_key] = {
                "当前周": current_val_str,
                "过去4周均值": prev_val_str,
                "变化率": f"{change_val:.2f}%" if pd.notna(change_val) else '0.00%',
                "current_raw": current_val,
                "previous_raw": prev_val,
                "change_raw": change_val
            }

        # 2. 完整的充值分布数据（不再只是摘要）
        full_recharge_distribution_json = "{}"
        if recharge_distribution is not None and not isinstance(recharge_distribution, bool):
            try:
                full_recharge_data = {}
                if 'all_data' in recharge_distribution and not recharge_distribution['all_data'].empty:
                    all_weeks_data = recharge_distribution['all_data']
                    full_recharge_data['historical_data'] = all_weeks_data.to_dict('records')
                
                if 'latest_data' in recharge_distribution and not recharge_distribution['latest_data'].empty:
                    latest_data = recharge_distribution['latest_data']
                    full_recharge_data['latest_week_data'] = latest_data.to_dict('records')
                
                full_recharge_data = convert_numpy_types(full_recharge_data)
                full_recharge_distribution_json = json.dumps(full_recharge_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整充值分布数据时出错: {e}")

        # 3. 完整的活动数据（而非只是最新周数据）
        full_activity_analysis_json = "{}"
        if activity_analysis is not None and not isinstance(activity_analysis, bool) and not activity_analysis.empty:
            try:
                activity_full_data = activity_analysis.to_dict('records')
                activity_full_data = convert_numpy_types(activity_full_data)
                full_activity_analysis_json = json.dumps(activity_full_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整活动数据时出错: {e}")

        # 4. 完整的活动付费数据
        full_activity2_analysis_json = "{}"
        if activity2_analysis is not None and not isinstance(activity2_analysis, bool) and not activity2_analysis.empty:
            try:
                activity2_full_data = activity2_analysis.to_dict('records')
                activity2_full_data = convert_numpy_types(activity2_full_data)
                full_activity2_analysis_json = json.dumps(activity2_full_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整活动付费数据时出错: {e}")

        # 5. 完整的养成线数据
        full_cultivation_analysis_json = "{}"
        if cultivation_analysis is not None and not isinstance(cultivation_analysis, bool) and not cultivation_analysis.empty:
            try:
                cultivation_full_data = cultivation_analysis.to_dict('records')
                cultivation_full_data = convert_numpy_types(cultivation_full_data)
                full_cultivation_analysis_json = json.dumps(cultivation_full_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整养成线数据时出错: {e}")

        # 6. 完整的TOP用户数据（不限制前10名）
        full_top20_analysis_json = "{}"
        if top20_analysis is not None and not isinstance(top20_analysis, bool) and not top20_analysis.empty:
            try:
                # 获取完整的TOP用户数据，而非只有前10名
                full_top_users = top20_analysis  # 获取全部用户数据
                if not full_top_users.empty:
                    required_cols = ['角色ID', '角色名', '累计充值金额']
                    if all(col in full_top_users.columns for col in required_cols):
                        top_users_data = []
                        for i, row in full_top_users.iterrows():
                            user_data = {
                                '排名': i+1,
                                '角色ID': str(row['角色ID']),
                                '角色名': str(row['角色名']),
                                '累计充值金额': int(row['累计充值金额'])
                            }
                            # 添加其他所有可用列
                            for col in full_top_users.columns:
                                if col not in required_cols and pd.notna(row[col]):
                                    if re.match(r'\d{4}-\d{2}-\d{2}', str(col)):
                                        user_data[f'周{col}'] = int(row[col])
                                    else:
                                        user_data[col] = str(row[col])
                            top_users_data.append(user_data)
                        top_users_data = convert_numpy_types(top_users_data)
                        full_top20_analysis_json = json.dumps(top_users_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整TOP用户数据时出错: {e}")

        # 7. 完整的新用户TOP数据
        full_new_top_analysis_json = "{}"
        if new_top_analysis is not None and not isinstance(new_top_analysis, bool) and not new_top_analysis.empty:
            try:
                # 获取完整的新用户TOP数据
                full_new_top_users = new_top_analysis
                if not full_new_top_users.empty:
                    required_cols = ['角色ID', '角色名', '累计充值金额']
                    if all(col in full_new_top_users.columns for col in required_cols):
                        new_top_users_data = []
                        for i, row in full_new_top_users.iterrows():
                            user_data = {
                                '排名': i+1,
                                '角色ID': str(row['角色ID']),
                                '角色名': str(row['角色名']),
                                '累计充值金额': int(row['累计充值金额'])
                            }
                            # 添加其他所有可用列
                            for col in full_new_top_users.columns:
                                if col not in required_cols and pd.notna(row[col]):
                                    if re.match(r'\d{4}-\d{2}-\d{2}', str(col)):
                                        user_data[f'周{col}'] = int(row[col])
                                    else:
                                        user_data[col] = str(row[col])
                            new_top_users_data.append(user_data)
                        new_top_users_data = convert_numpy_types(new_top_users_data)
                        full_new_top_analysis_json = json.dumps(new_top_users_data, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理完整新用户TOP数据时出错: {e}")

        # 8. 添加原始数据上下文（如果提供）
        raw_data_context_json = "{}"
        if raw_data_context is not None:
            try:
                raw_data_context_json = json.dumps(convert_numpy_types(raw_data_context), ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"处理原始数据上下文时出错: {e}")

        all_metrics_converted = convert_numpy_types(all_metrics)
        
        prompt = ANALYSIS_PROMPT_TEMPLATE.format(
            channel_name=channel_name,
            current_date_range=current_date_range,
            previous_date_range=previous_date_range,
            new_user_metrics_text="完整新用户数据已包含在全量指标中",
            basic_metrics_json=json.dumps(all_metrics_converted, ensure_ascii=False, indent=2),
            tier_metrics_json="{}",  # 已包含在all_metrics中
            old_user_metrics_json="{}",  # 已包含在all_metrics中
            formatted_anomalies="\n".join(formatted_anomalies) if formatted_anomalies else f"无显著异常指标（所有指标平均变化率均在{ANOMALY_THRESHOLD}%以内）",
            threshold=ANOMALY_THRESHOLD,
            recharge_distribution_json=full_recharge_distribution_json,
            activity_analysis_json=full_activity_analysis_json,
            activity2_analysis_json=full_activity2_analysis_json,
            cultivation_analysis_json=full_cultivation_analysis_json,
            top20_analysis_json=full_top20_analysis_json,
            new_top_analysis_json=full_new_top_analysis_json
        )
        
        # 添加完整数据集到提示中
        prompt += f"\n\n**完整数据集：**\n"
        prompt += f"**所有指标数据：**\n{json.dumps(all_metrics_converted, ensure_ascii=False, indent=2)}\n"
        prompt += f"**完整充值分布数据：**\n{full_recharge_distribution_json}\n"
        prompt += f"**完整活动数据：**\n{full_activity_analysis_json}\n"
        prompt += f"**完整活动付费数据：**\n{full_activity2_analysis_json}\n"
        prompt += f"**完整养成线数据：**\n{full_cultivation_analysis_json}\n"
        prompt += f"**完整TOP用户数据：**\n{full_top20_analysis_json}\n"
        prompt += f"**完整新用户TOP数据：**\n{full_new_top_analysis_json}\n"
        
        if raw_data_context_json != "{}":
            prompt += f"**原始数据上下文：**\n{raw_data_context_json}\n"

        if api_data is not None:
            prompt += "\n\n**API数据：**\n"
            prompt += f"使用{api_version.upper()}版本API数据进行分析"

        # 重试机制开始
        current_delay = RETRY_DELAY  # 保存初始延迟值
        last_error = None
        
        for retry_attempt in range(MAX_RETRIES):
            try:
                print(f"🔄 正在尝试调用OpenAI API (第{retry_attempt + 1}次/共{MAX_RETRIES}次)...")
                
                # 如果不是第一次尝试，进行网络连接测试
                if retry_attempt > 0:
                    print("🔍 进行网络连接诊断...")
                    test_network_connectivity()
                
                messages = [
                    {"role": "system", "content": AI_SYSTEM_PROMPT},
                    {"role": "user", "content": prompt}
                ]
                
                # 准备完整的API请求数据用于保存
                api_request_data = {
                    "model": current_config["MODEL"],
                    "messages": messages,
                    "temperature": current_config["TEMPERATURE"],
                    "timeout": TIMEOUT_SECONDS
                }
                
                # 根据模型类型添加正确的token限制参数
                if "o3" in current_config["MODEL"].lower():
                    api_request_data["max_completion_tokens"] = current_config["MAX_TOKENS"]
                else:
                    api_request_data["max_tokens"] = current_config["MAX_TOKENS"]
                
                # =============================================
                # 保存AI API的完整输入内容到aiapi文件夹
                # =============================================
                try:
                    analysis_date = last_7_days.get('start_date', '')
                    save_ai_api_content(
                        channel_name=channel_name,
                        system_prompt=AI_SYSTEM_PROMPT,
                        user_prompt=prompt,
                        api_request_data=api_request_data,
                        analysis_date=analysis_date
                    )
                except Exception as save_error:
                    print(f"⚠️ 保存AI API内容时出错（但不影响分析继续）: {save_error}")
                
                # 记录请求开始时间
                start_time = time.time()
                
                # 实际调用API
                # o3系列模型需要使用max_completion_tokens而不是max_tokens
                api_params = {
                    "model": current_config["MODEL"],
                    "messages": messages,
                    "temperature": current_config["TEMPERATURE"],
                    "timeout": TIMEOUT_SECONDS
                }
                
                # 根据模型类型选择正确的token限制参数
                if "o3" in current_config["MODEL"].lower():
                    api_params["max_completion_tokens"] = current_config["MAX_TOKENS"]
                else:
                    api_params["max_tokens"] = current_config["MAX_TOKENS"]
                
                response = client.chat.completions.create(**api_params)
                
                # 记录请求完成时间
                end_time = time.time()
                request_duration = end_time - start_time
                
                if hasattr(response, 'choices') and response.choices and hasattr(response.choices[0], 'message'):
                    analysis = response.choices[0].message.content
                    analysis = analysis.strip().replace("```json", "").replace("```", "")
                    print(f"✅ OpenAI API调用成功! 耗时: {request_duration:.2f}秒")
                    return analysis
                else:
                    print(f"❌ OpenAI API 响应无效或为空。响应内容: {response}")
                    if retry_attempt < MAX_RETRIES - 1:
                        print(f"⏱️ {current_delay}秒后重试...")
                        time.sleep(current_delay)
                        if USE_EXPONENTIAL_BACKOFF:
                            current_delay *= 2  # 指数退避
                        continue
                    else:
                        return generate_fallback_analysis(channel_name, metrics_total, anomalies)
                
            except (APIConnectionError, ConnectError, RemoteProtocolError, ssl.SSLEOFError) as e:
                error_type = type(e).__name__
                last_error = e
                print(f"🔌 网络连接错误 ({error_type}): {str(e)}")
                
                # 详细错误诊断
                if "SSL" in str(e):
                    print("🔒 SSL握手失败，可能是网络防火墙或证书问题")
                elif "RemoteProtocolError" in error_type:
                    print("📡 服务器端异常关闭连接，可能是服务器负载过高")
                elif "ConnectError" in error_type:
                    print("🌐 网络连接建立失败，请检查网络状况")
                
                if retry_attempt < MAX_RETRIES - 1:
                    print(f"⏱️ 网络问题，{current_delay}秒后重试...")
                    time.sleep(current_delay)
                    if USE_EXPONENTIAL_BACKOFF:
                        current_delay *= 2  # 指数退避，下次等待更长时间
                    continue
                else:
                    print(f"❌ 重试{MAX_RETRIES}次后仍然连接失败，使用备用分析")
                    print(f"💡 建议: 1)检查网络连接 2)检查防火墙设置 3)稍后再试")
                    return generate_fallback_analysis(channel_name, metrics_total, anomalies)
                    
            except (APITimeoutError, TimeoutException) as e:
                last_error = e
                print(f"⏰ API超时错误: {str(e)}")
                print(f"🕐 当前超时设置: {TIMEOUT_SECONDS}秒")
                if retry_attempt < MAX_RETRIES - 1:
                    print(f"⏱️ 请求超时，{current_delay}秒后重试...")
                    time.sleep(current_delay)
                    if USE_EXPONENTIAL_BACKOFF:
                        current_delay *= 2
                    continue
                else:
                    print(f"❌ 重试{MAX_RETRIES}次后仍然超时，使用备用分析")
                    print(f"💡 建议: 1)检查网络速度 2)增加超时设置 3)稍后再试")
                    return generate_fallback_analysis(channel_name, metrics_total, anomalies)
                    
            except Exception as e:
                last_error = e
                error_msg = f"未知错误: {str(e)}"
                print(f"❓ {error_msg}")
                print(f"🔍 详细错误信息:")
                print(traceback.format_exc())
                if retry_attempt < MAX_RETRIES - 1:
                    print(f"⏱️ 发生未知错误，{current_delay}秒后重试...")
                    time.sleep(current_delay)
                    if USE_EXPONENTIAL_BACKOFF:
                        current_delay *= 2
                    continue
                else:
                    print(f"❌ 重试{MAX_RETRIES}次后仍然失败，使用备用分析")
                    return generate_fallback_analysis(channel_name, metrics_total, anomalies)
        
        # 如果所有重试都失败了
        print(f"❌ 所有重试尝试失败，最后一次错误: {last_error}")
        return generate_fallback_analysis(channel_name, metrics_total, anomalies)
                    
    except Exception as e:
        error_msg = f"使用AI分析数据时出错: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return generate_fallback_analysis(channel_name, metrics_total, anomalies)

def generate_fallback_analysis(channel_name, metrics_total, anomalies):
    try:
        metrics_total = convert_numpy_types(metrics_total)
        current_revenue, previous_revenue, revenue_change = 0, 0, 0
        if '流水' in metrics_total:
            data = metrics_total['流水']
            current_revenue = data.get('current', 0)
            previous_revenue = data.get('previous', 0)
            revenue_change = data.get('growth_rate', 0)
        elif '日流水' in metrics_total:
            data = metrics_total['日流水']
            current_revenue = data.get('current', 0)
            previous_revenue = data.get('previous', 0)
            revenue_change = data.get('growth_rate', 0)
        default_text = f"本周{channel_name}渠道总流水{current_revenue:,.1f}元，相比过去4周均值变化{revenue_change:.1f}%。"
        if anomalies:
            default_text += f" 发现{len(anomalies)}项异常指标，请查看异常指标汇总了解详情。"
        else:
            default_text += " 各项指标表现稳定，无重大波动。"
        return default_text
    except Exception as e:
        return f"{channel_name}渠道数据分析生成失败: {str(e)}"

def create_trend_chart(dates, values, previous_dates, previous_values, title, y_label, figsize=None):
    if figsize is None:
        figsize = VISUALIZATION_CONFIG.get("FIGURE_SIZE_TREND", (9, 4))
    fig, ax = plt.subplots(figsize=figsize)
    plt.tight_layout()
    return fig

def _define_excel_formats(workbook):
    formats = {}
    base_font = 'Microsoft YaHei'
    primary_color = '#2C5F8E'
    primary_light = '#E8EFF5'
    primary_dark = '#1A3A57'
    primary_accent = '#5B9BD5'
    neutral_color = '#F9F9F9'
    neutral_border = '#E0E0E0'
    positive_color = '#EBF5EB'
    negative_color = '#FFF1F0'
    positive_text = '#1E6E1E'
    negative_text = '#D64541'
    cell_base = {
        'font_name': base_font,
        'border': 1,
        'border_color': neutral_border,
        'valign': 'vcenter',
        'align': 'center'
    }
    formats['alt_row'] = workbook.add_format({**cell_base, 'bg_color': neutral_color})
    
    # 添加带数字格式的交替行格式
    formats['alt_row_integer'] = workbook.add_format({**cell_base, 'bg_color': neutral_color, 'num_format': '#,##0'})
    formats['alt_row_number'] = workbook.add_format({**cell_base, 'bg_color': neutral_color, 'num_format': '#,##0.0'})
    formats['alt_row_percent'] = workbook.add_format({**cell_base, 'bg_color': neutral_color, 'num_format': '0.0%'})
    
    formats['title'] = workbook.add_format({
        'bold': True,
        'font_size': 16,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': primary_dark,
        'font_color': 'white',
        'font_name': base_font,
        'border': 0,
        'bottom': 0
    })
    formats['subtitle'] = workbook.add_format({
        'bold': True,
        'font_size': 12,
        'bg_color': primary_color,
        'valign': 'vcenter',
        'font_name': base_font,
        'border': 0,
        'font_color': 'white',
        'align': 'center',
        'bottom': 0
    })
    formats['header'] = workbook.add_format({
        'bold': True,
        'bg_color': primary_light,
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'font_name': base_font,
        'text_wrap': True,
        'font_color': primary_dark,
        'bottom': 1,
        'bottom_color': primary_accent,
        'border_color': neutral_border
    })
    formats['text'] = workbook.add_format({
        'text_wrap': True,
        'valign': 'top',
        'font_name': base_font
    })
    formats['wrap_center'] = workbook.add_format({**cell_base, 'text_wrap': True})
    formats['number'] = workbook.add_format({**cell_base, 'num_format': '#,##0.0'})
    formats['integer'] = workbook.add_format({**cell_base, 'num_format': '#,##0'})
    formats['integer_1d'] = workbook.add_format({**cell_base, 'num_format': '#,##0.0'})
    formats['percent'] = workbook.add_format({**cell_base, 'num_format': '0.0%'})
    formats['percent_int'] = workbook.add_format({**cell_base, 'num_format': '0%'})
    formats['date'] = workbook.add_format({**cell_base, 'num_format': 'yyyy-mm-dd'})
    summary_base = {
        **cell_base,
        'bold': True,
        'bg_color': primary_light,
        'font_color': primary_dark,
        'top': 1,
        'top_color': primary_accent
    }
    formats['summary_text'] = workbook.add_format(summary_base)
    formats['summary_number'] = workbook.add_format({**summary_base, 'num_format': '#,##0.0'})
    formats['summary_integer'] = workbook.add_format({**summary_base, 'num_format': '#,##0'})
    formats['summary_percent'] = workbook.add_format({**summary_base, 'num_format': '0.0%'})
    formats['top20_cell'] = workbook.add_format(cell_base)
    formats['top20_number'] = workbook.add_format({**cell_base, 'num_format': '#,##0.0'})
    formats['top20_integer'] = workbook.add_format({**cell_base, 'num_format': '#,##0'})
    formats['top20_highlight'] = workbook.add_format({**cell_base, 'num_format': '#,##0.0', 'bold': True})
    change_base = {**cell_base, 'num_format': '0%'}
    formats['change_pos'] = workbook.add_format({**change_base, 'font_color': positive_text, 'bg_color': positive_color})
    formats['change_neg'] = workbook.add_format({**change_base, 'font_color': negative_text, 'bg_color': negative_color})
    formats['change_neu'] = workbook.add_format({**change_base})
    summary_change_base = {**summary_base, 'num_format': '0%'}
    formats['summary_change_pos'] = workbook.add_format({**summary_change_base, 'font_color': positive_text, 'bg_color': positive_color})
    formats['summary_change_neg'] = workbook.add_format({**summary_change_base, 'font_color': negative_text, 'bg_color': negative_color})
    formats['summary_change_neu'] = workbook.add_format(summary_change_base)
    return formats

def _get_value_format(metric_name, is_summary=False):
    prefix = 'summary_' if is_summary else ''
    if '率' in metric_name:
        return f'{prefix}percent'
    elif metric_name == 'DAU' or metric_name == '日DAU':
        return f'{prefix}integer'
    elif metric_name == '流水' or metric_name == '日流水':
        return f'{prefix}integer'
    elif metric_name == '新增ARPU' or metric_name == '付费 ARPU' or 'ARPPU' in metric_name:
        return f'{prefix}number'
    elif '人数' in metric_name or metric_name == '新增':
        return f'{prefix}integer'
    else:
        return f'{prefix}number'

def _get_change_format(change_value, is_summary=False):
    prefix = 'summary_' if is_summary else ''
    if pd.isna(change_value): return f'{prefix}change_neu'
    try:
        change = float(change_value)
        if change > 0: return f'{prefix}change_pos'
        elif change < 0: return f'{prefix}change_neg'
        else: return f'{prefix}change_neu'
    except (ValueError, TypeError):
        return f'{prefix}change_neu'

def _write_title_and_summary(worksheet, result, formats, current_row):
    channel_name = result.get('channel', '未知渠道')
    analysis_period = result.get('last_7_days', {}).get('start_date', '未知周期')
    display_name = channel_name
    worksheet.merge_range(current_row, 0, current_row, 22, f'{display_name} 周数据分析报告 ({analysis_period})', formats['title'])
    worksheet.set_row(current_row, 35)
    current_row += 2
    worksheet.merge_range(current_row, 0, current_row, 7, '数据分析概要', formats['subtitle'])
    worksheet.set_row(current_row, 25)
    current_row += 1
    analysis_text = result.get('analysis_text', 'AI分析未生成或失败。')
    text_length = len(analysis_text)
    chars_per_line = 80
    min_lines = 5
    estimated_lines = max(min_lines, (text_length // chars_per_line) + 3)
    newline_count = analysis_text.count('\n')
    if newline_count > 0:
        estimated_lines = max(estimated_lines, newline_count + 3)
    worksheet.merge_range(current_row, 0, current_row + estimated_lines - 1, 22, analysis_text, formats['text'])
    for r in range(current_row, current_row + estimated_lines):
        worksheet.set_row(r, 18)
    current_row += estimated_lines + 1
    return current_row

def _write_anomalies(worksheet, result, formats, current_row):
    metrics_total = result.get('all_metrics_comparison', {})
    threshold = ANOMALY_THRESHOLD
    original_data = result.get('original_data')
    api_averages = result.get('api_averages')
    latest_week_data = None
    if original_data is not None and not original_data.empty:
        latest_week_data = original_data.sort_values(by='日期', ascending=True).iloc[-1]
    api_weekly_data = api_averages.get('weekly_data', []) if api_averages else []
    api_data_map = {}
    for item in api_weekly_data:
        try:
            date_obj = pd.to_datetime(item['日期'])
            week_start = date_obj - pd.Timedelta(days=date_obj.weekday())
            api_data_map[week_start.strftime('%Y-%m-%d')] = item
            date_str = date_obj.strftime('%Y-%m-%d')
            api_data_map[date_str] = item
        except Exception as e:
            pass
    all_anomalies = []
    for metric, comparison_data in metrics_total.items():
        growth_rate = comparison_data.get('growth_rate')
        if growth_rate is not None and pd.notna(growth_rate) and abs(growth_rate) >= threshold:
            current_value = None
            if metric in ['DAU', '付费率'] and latest_week_data is not None:
                latest_date = latest_week_data.get('日期')
                if latest_date is not None:
                    api_row = None
                    latest_date_str = latest_date.strftime('%Y-%m-%d')
                    if latest_date_str in api_data_map:
                        api_row = api_data_map.get(latest_date_str)
                    if api_row is None:
                        week_start = latest_date - pd.Timedelta(days=latest_date.weekday())
                        week_start_str = week_start.strftime('%Y-%m-%d')
                        api_row = api_data_map.get(week_start_str)
                    if api_row is None and api_weekly_data:
                        api_dates = [pd.to_datetime(item['日期']) for item in api_weekly_data]
                        date_diffs = [(abs((latest_date - d).days), i) for i, d in enumerate(api_dates)]
                        closest_idx = min(date_diffs, key=lambda x: x[0])[1]
                        api_row = api_weekly_data[closest_idx]
                    if api_row:
                        api_metric = 'DAU' if metric == 'DAU' else '付费率'
                        if api_metric in api_row:
                            current_value = api_row.get(api_metric)
            if current_value is None:
                metric_mapping = {
                    '流水': '日流水',
                    '付费 ARPU': 'ARPU',
                    'DAU': '日DAU'
                }
                actual_metric = metric_mapping.get(metric, metric)
                if latest_week_data is not None:
                    if actual_metric in latest_week_data:
                        current_value = latest_week_data[actual_metric]
                    elif metric in latest_week_data:
                        current_value = latest_week_data[metric]
            if current_value is None:
                current_value = comparison_data.get('current')
            all_anomalies.append({
                '指标': metric,
                '当期平均': current_value,
                '上周平均': comparison_data.get('previous'),
                '变化率(%)': growth_rate,
                '方向': '增长' if growth_rate > 0 else '下降'
            })
    all_anomalies.sort(key=lambda x: abs(x.get('变化率(%)', 0)), reverse=True)
    worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', f'异常指标汇总 (变化率绝对值 > {threshold}%)', formats['subtitle'])
    worksheet.set_row(current_row, 25)
    current_row += 1
    if all_anomalies:
        headers = ['指标', '当期值', '过去4周均值', '变化率(%)']
        col_widths = [25, 15, 15, 15]
        for col, header in enumerate(headers):
            worksheet.write(current_row, col, header, formats['header'])
            worksheet.set_column(col, col, col_widths[col])
        table_start_row = current_row
        current_row += 1
        for anomaly in all_anomalies:
            metric_name = anomaly.get('指标', '未知指标')
            current_val = anomaly.get('当期平均', 'N/A')
            previous_val = anomaly.get('上周平均', 'N/A')
            change_pct = anomaly.get('变化率(%)', 'N/A')
            val_fmt_name = _get_value_format(metric_name)
            val_fmt = formats.get(val_fmt_name, formats['wrap_center'])
            change_fmt_name = _get_change_format(change_pct)
            change_fmt = formats.get(change_fmt_name, formats['wrap_center'])
            worksheet.write(current_row, 0, metric_name, formats['wrap_center'])
            worksheet.write(current_row, 1, current_val, val_fmt if pd.notna(current_val) else formats['wrap_center'])
            worksheet.write(current_row, 2, previous_val, val_fmt if pd.notna(previous_val) else formats['wrap_center'])
            if pd.notna(change_pct) and isinstance(change_pct, (int, float)):
                if not np.isfinite(change_pct):
                    worksheet.write(current_row, 3, 'N/A', formats['wrap_center'])
                else:
                    worksheet.write_number(current_row, 3, change_pct / 100.0, change_fmt)
            else:
                worksheet.write(current_row, 3, 'N/A', formats['wrap_center'])
            current_row += 1
        table_end_row = current_row - 1
        if table_end_row >= table_start_row:
            cell_range = xlsxwriter.utility.xl_range(table_start_row, 0, table_end_row, len(headers) - 1)
            worksheet.conditional_format(cell_range, {
                'type': 'no_blanks',
                'format': formats['wrap_center']
            })
    else:
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', f'无显著异常指标 (变化率绝对值均 <= {threshold}%)', formats['wrap_center'])
        current_row += 1
    current_row += 1
    return current_row

def _write_basic_data_comparison(worksheet, result, formats, current_row):
    original_data = result.get('original_data')
    metrics_total = result.get('metrics_total', {})
    api_averages = result.get('api_averages')
    if original_data is None or original_data.empty:
        return current_row
    recent_5_weeks = original_data.sort_values(by='日期', ascending=True).tail(5)
    if recent_5_weeks.empty: return current_row
    has_new_pay_rate = '新增付费率' in recent_5_weeks.columns
    has_new_arppu = '新增ARPPU' in recent_5_weeks.columns
    start_row = current_row
    worksheet.merge_range(start_row, 0, start_row, 2, '新增数据对比', formats['subtitle'])
    worksheet.set_row(start_row, 25)
    start_row += 1
    if not has_new_pay_rate and '新增' in recent_5_weeks.columns and '新增付费' in recent_5_weeks.columns:
        try:
            recent_5_weeks['新增付费率'] = recent_5_weeks.apply(
                lambda row: row['新增付费'] / row['新增'] if row['新增'] > 0 else 0,
                axis=1
            )
            has_new_pay_rate = True
        except Exception as e:
            pass
    if not has_new_arppu and '新增付费金额' in recent_5_weeks.columns and '新增付费' in recent_5_weeks.columns:
        try:
            recent_5_weeks['新增ARPPU'] = recent_5_weeks.apply(
                lambda row: row['新增付费金额'] / row['新增付费'] if row['新增付费'] > 0 else 0,
                axis=1
            )
            has_new_arppu = True
        except Exception as e:
            pass
    if not has_new_pay_rate and '新增付费率' in original_data.columns:
        recent_5_weeks['新增付费率'] = original_data['新增付费率']
        has_new_pay_rate = True
    if not has_new_arppu and '新增ARPPU' in original_data.columns:
        recent_5_weeks['新增ARPPU'] = original_data['新增ARPPU']
        has_new_arppu = True
    if '付费 ARPU' in original_data.columns and 'ARPU' not in recent_5_weeks.columns:
        recent_5_weeks['ARPU'] = original_data['付费 ARPU']
    for tier in ['小', '中', '大', '超']:
        count_col = f'{tier} R 人数'
        amount_col = f'{tier} R 充值'
        if count_col in original_data.columns and count_col not in recent_5_weeks.columns:
            recent_5_weeks[count_col] = original_data[count_col]
        if amount_col in original_data.columns and amount_col not in recent_5_weeks.columns:
            recent_5_weeks[amount_col] = original_data[amount_col]
    if not has_new_pay_rate and not has_new_arppu:
        worksheet.merge_range(start_row, 0, start_row, 2, '无新增数据', formats['wrap_center'])
        start_row += 2
    new_headers = ['日期', '新增付费率', '新增ARPPU']
    for col, header in enumerate(new_headers):
        worksheet.write(start_row, col, header, formats['header'])
        worksheet.set_column(col, col, 12)
    start_row += 1
    has_new_pay_rate = '新增付费率' in recent_5_weeks.columns
    has_new_arppu = '新增ARPPU' in recent_5_weeks.columns
    if not has_new_pay_rate:
        if '新增' in recent_5_weeks.columns and '新增付费' in recent_5_weeks.columns:
            recent_5_weeks['新增付费率'] = recent_5_weeks.apply(
                lambda row: row['新增付费'] / row['新增'] if row['新增'] > 0 else 0,
                axis=1
            )
    if not has_new_arppu:
        if '新增付费金额' in recent_5_weeks.columns and '新增付费' in recent_5_weeks.columns:
            recent_5_weeks['新增ARPPU'] = recent_5_weeks.apply(
                lambda row: row['新增付费金额'] / row['新增付费'] if row['新增付费'] > 0 else 0,
                axis=1
            )
    data_start_row = start_row
    for i, (_, row_data) in enumerate(recent_5_weeks.iterrows()):
        row_num = data_start_row + i
        worksheet.write(row_num, 0, row_data.get('日期'), formats['date'] if pd.notna(row_data.get('日期')) else formats['wrap_center'])
        if has_new_pay_rate or '新增付费率' in row_data:
            val = row_data.get('新增付费率')
            worksheet.write(row_num, 1, val, formats['percent'] if pd.notna(val) else formats['wrap_center'])
        else:
            worksheet.write(row_num, 1, None, formats['wrap_center'])
        if has_new_arppu or '新增ARPPU' in row_data:
            val = row_data.get('新增ARPPU')
            worksheet.write(row_num, 2, val, formats['number'] if pd.notna(val) else formats['wrap_center'])
        else:
            worksheet.write(row_num, 2, None, formats['wrap_center'])
    mean_row = data_start_row + len(recent_5_weeks)
    change_row = mean_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    for col_idx, metric in enumerate(new_headers[1:], start=1):
        if metric in metrics_total:
            comp = metrics_total.get(metric, {})
            prev_val = comp.get('previous')
            change_val = comp.get('growth_rate')
            val_fmt_name = _get_value_format(metric, is_summary=True)
            change_fmt_name = _get_change_format(change_val, is_summary=True)
            worksheet.write(mean_row, col_idx, prev_val, formats.get(val_fmt_name, formats['summary_text']) if pd.notna(prev_val) else formats['summary_text'])
            if pd.notna(change_val) and np.isfinite(change_val):
                worksheet.write_number(change_row, col_idx, change_val / 100.0, formats.get(change_fmt_name, formats['summary_text']))
            else:
                worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
        else:
            worksheet.write(mean_row, col_idx, 'N/A', formats['summary_text'])
            worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
    base_col_offset = 4
    worksheet.merge_range(start_row - 2, base_col_offset, start_row - 2, base_col_offset + 5, '基础数据对比', formats['subtitle'])
    basic_headers = ['日期', '新增', '流水', '付费 ARPU', 'DAU', '付费率']
    basic_headers_mapping = {
        '流水': '日流水',
        '付费 ARPU': 'ARPU',
        'DAU': '日DAU'
    }
    for col, header in enumerate(basic_headers):
        worksheet.write(start_row - 1, col + base_col_offset, header, formats['header'])
        worksheet.set_column(col + base_col_offset, col + base_col_offset, 12)
    for i, (_, row_data) in enumerate(recent_5_weeks.iterrows()):
        row_num = data_start_row + i
        row_format = formats['alt_row'] if i % 2 == 1 else formats['wrap_center']
        date_format = formats['date'] if pd.notna(row_data.get('日期')) else row_format
        worksheet.write(row_num, base_col_offset, row_data.get('日期'), date_format)
        for col_idx, metric in enumerate(basic_headers[1:4], start=1):
            actual_metric = basic_headers_mapping.get(metric, metric)
            val = row_data.get(actual_metric)
            if val is None and actual_metric != metric:
                val = row_data.get(metric)
            if val is None:
                if actual_metric in original_data.columns:
                    val = original_data.iloc[i].get(actual_metric)
                elif metric in original_data.columns:
                    val = original_data.iloc[i].get(metric)
            fmt_name = _get_value_format(metric)
            if pd.notna(val):
                if i % 2 == 1:
                    # 使用带数字格式的交替行格式
                    if '率' in metric:
                        worksheet.write(row_num, col_idx + base_col_offset, val, formats['alt_row_percent'])
                    elif metric in ['流水', '日流水', 'DAU', '日DAU', '新增']:
                        worksheet.write(row_num, col_idx + base_col_offset, val, formats['alt_row_integer'])
                    else:
                        worksheet.write(row_num, col_idx + base_col_offset, val, formats['alt_row_number'])
                else:
                    worksheet.write(row_num, col_idx + base_col_offset, val, formats.get(fmt_name, formats['wrap_center']))
            else:
                row_format = formats['alt_row'] if i % 2 == 1 else formats['wrap_center']
                worksheet.write(row_num, col_idx + base_col_offset, None, row_format)
    api_weekly_data = api_averages.get('weekly_data', []) if api_averages else []
    api_data_map = {}
    for item in api_weekly_data:
        try:
            date_obj = pd.to_datetime(item['日期'])
            week_start = date_obj - pd.Timedelta(days=date_obj.weekday())
            api_data_map[week_start.strftime('%Y-%m-%d')] = item
            date_str = date_obj.strftime('%Y-%m-%d')
            api_data_map[date_str] = item
        except Exception as e:
            pass
    for i, (_, row_data) in enumerate(recent_5_weeks.iterrows()):
        row_num = data_start_row + i
        row_date = row_data['日期']
        api_row = None
        row_date_str = row_date.strftime('%Y-%m-%d')
        if row_date_str in api_data_map:
            api_row = api_data_map.get(row_date_str)
        if api_row is None:
            week_start = row_date - pd.Timedelta(days=row_date.weekday())
            week_start_str = week_start.strftime('%Y-%m-%d')
            api_row = api_data_map.get(week_start_str)
        if api_row is None and api_weekly_data:
            api_dates = [pd.to_datetime(item['日期']) for item in api_weekly_data]
            date_diffs = [(abs((row_date - d).days), i) for i, d in enumerate(api_dates)]
            closest_idx = min(date_diffs, key=lambda x: x[0])[1]
            api_row = api_weekly_data[closest_idx]
        week_start_str = 'N/A'
        if 'week_start' in locals():
            week_start_str = week_start.strftime('%Y-%m-%d')
        dau_val = None
        if api_row and 'DAU' in api_row:
            dau_val = api_row.get('DAU')
        if dau_val is not None:
            try:
                dau_val = float(dau_val)
            except (ValueError, TypeError) as e:
                dau_val = None
        worksheet.write(row_num, 4 + base_col_offset, dau_val, formats['integer'] if pd.notna(dau_val) else formats['wrap_center'])
        rate_val = None
        if api_row and '付费率' in api_row:
            rate_val = api_row.get('付费率')
        if rate_val is not None:
            try:
                rate_val = float(rate_val)
                if rate_val > 1:
                    rate_val = rate_val / 100.0
            except (ValueError, TypeError):
                rate_val = None
        worksheet.write(row_num, 5 + base_col_offset, rate_val, formats['percent'] if pd.notna(rate_val) else formats['wrap_center'])
    worksheet.write(mean_row, base_col_offset, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, base_col_offset, "变化率", formats['summary_text'])
    
    # 为基础数据指标写入过去4周均值和变化率
    basic_metrics = ['新增', '日流水', 'ARPU', 'DAU', '付费率']  # 对应表头中的指标
    basic_metrics_mapping = {
        '新增': '新增',
        '日流水': '日流水', 
        'ARPU': '付费 ARPU',
        'DAU': 'DAU',
        '付费率': '付费率'
    }
    
    for col_idx, metric in enumerate(basic_metrics, start=1):
        mapped_metric = basic_metrics_mapping.get(metric, metric)
        comp = metrics_total.get(mapped_metric, {})
        prev_val = comp.get('previous')
        change_val = comp.get('growth_rate')
        
        val_fmt_name = _get_value_format(mapped_metric, is_summary=True)
        change_fmt_name = _get_change_format(change_val, is_summary=True)
        
        worksheet.write(mean_row, col_idx + base_col_offset, prev_val, formats.get(val_fmt_name, formats['summary_text']) if pd.notna(prev_val) else formats['summary_text'])
        
        if pd.notna(change_val) and np.isfinite(change_val):
            worksheet.write_number(change_row, col_idx + base_col_offset, change_val / 100.0, formats.get(change_fmt_name, formats['summary_text']))
        else:
            worksheet.write(change_row, col_idx + base_col_offset, 'N/A', formats['summary_text'])
    
    return change_row + 2

def _write_payment_tiers(worksheet, result, formats, current_row):
    original_data = result.get('original_data')
    metrics_total = result.get('metrics_total', {})
    if original_data is None or original_data.empty:
        return current_row
    recent_5_weeks = original_data.sort_values(by='日期', ascending=True).tail(5)
    if recent_5_weeks.empty: return current_row
    tier_columns = [col for col in recent_5_weeks.columns if any(tier in col for tier in ['R人数', 'R充值', ' R 人数', ' R 充值'])]
    for tier in ['小', '中', '大', '超']:
        count_col = f'{tier} R 人数'
        amount_col = f'{tier} R 充值'
        if count_col in original_data.columns and count_col not in recent_5_weeks.columns:
            recent_5_weeks[count_col] = original_data[count_col]
            tier_columns.append(count_col)
        if amount_col in original_data.columns and amount_col not in recent_5_weeks.columns:
            recent_5_weeks[amount_col] = original_data[amount_col]
            tier_columns.append(amount_col)
    if not tier_columns:
        worksheet.merge_range(current_row, 0, current_row, 10, '付费层级分析', formats['subtitle'])
        worksheet.set_row(current_row, 20)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无付费层级数据', formats['wrap_center'])
        return current_row + 2
    start_row = current_row
    worksheet.merge_range(start_row, 0, start_row, 10, '付费层级分析', formats['subtitle'])
    worksheet.set_row(start_row, 20)
    start_row += 1
    count_headers = ['日期']
    amount_headers = ['日期']
    for tier in ['小', '中', '大', '超']:
        count_col = f'{tier} R 人数'
        amount_col = f'{tier} R 充值'
        if count_col in recent_5_weeks.columns:
            count_headers.append(count_col)
        if amount_col in recent_5_weeks.columns:
            amount_headers.append(amount_col)
    amount_col_offset = len(count_headers) + 1
    for col, header in enumerate(count_headers):
        worksheet.write(start_row, col, header, formats['header'])
        worksheet.set_column(col, col, 12)
    for col, header in enumerate(amount_headers):
        worksheet.write(start_row, col + amount_col_offset, header, formats['header'])
        worksheet.set_column(col + amount_col_offset, col + amount_col_offset, 12)
    start_row += 1
    data_start_row = start_row
    for i, (_, row_data) in enumerate(recent_5_weeks.iterrows()):
        row_num = data_start_row + i
        worksheet.write(row_num, 0, row_data.get('日期'), formats['date'] if pd.notna(row_data.get('日期')) else formats['wrap_center'])
        for col_idx, metric in enumerate(count_headers[1:], start=1):
            val = row_data.get(metric)
            worksheet.write(row_num, col_idx, val, formats['integer'] if pd.notna(val) else formats['wrap_center'])
        worksheet.write(row_num, amount_col_offset, row_data.get('日期'), formats['date'] if pd.notna(row_data.get('日期')) else formats['wrap_center'])
        for col_idx, metric in enumerate(amount_headers[1:], start=1):
            val = row_data.get(metric)
            worksheet.write(row_num, col_idx + amount_col_offset, val, formats['integer'] if pd.notna(val) else formats['wrap_center'])
    mean_row = data_start_row + len(recent_5_weeks)
    change_row = mean_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    worksheet.write(mean_row, amount_col_offset, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, amount_col_offset, "变化率", formats['summary_text'])
    for col_idx, metric in enumerate(count_headers[1:], start=1):
        comp = metrics_total.get(metric, {})
        prev_val = comp.get('previous')
        change_val = comp.get('growth_rate')
        val_fmt_name = _get_value_format(metric, is_summary=True)
        change_fmt_name = _get_change_format(change_val, is_summary=True)
        worksheet.write(mean_row, col_idx, prev_val, formats.get(val_fmt_name, formats['summary_text']) if pd.notna(prev_val) else formats['summary_text'])
        if pd.notna(change_val):
            worksheet.write_number(change_row, col_idx, change_val / 100.0, formats.get(change_fmt_name, formats['summary_text']))
        else:
            worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
    for col_idx, metric in enumerate(amount_headers[1:], start=1):
        comp = metrics_total.get(metric, {})
        prev_val = comp.get('previous')
        change_val = comp.get('growth_rate')
        val_fmt_name = _get_value_format(metric, is_summary=True)
        change_fmt_name = _get_change_format(change_val, is_summary=True)
        if metric.endswith(' R 充值'):
            worksheet.write(mean_row, col_idx + amount_col_offset, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
        else:
            worksheet.write(mean_row, col_idx + amount_col_offset, prev_val, formats.get(val_fmt_name, formats['summary_text']) if pd.notna(prev_val) else formats['summary_text'])
        if pd.notna(change_val):
            worksheet.write_number(change_row, col_idx + amount_col_offset, change_val / 100.0, formats.get(change_fmt_name, formats['summary_text']))
        else:
            worksheet.write(change_row, col_idx + amount_col_offset, 'N/A', formats['summary_text'])
    return change_row + 2

def _write_recharge_distribution(worksheet, result, formats, current_row):
    rc_dist_data = result.get('recharge_distribution')
    if not rc_dist_data or 'all_data' not in rc_dist_data or rc_dist_data['all_data'].empty:
        total_columns = 15
        worksheet.merge_range(current_row, 0, current_row, total_columns - 1, '付费金额结构', formats['subtitle'])
        worksheet.set_row(current_row, 20)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无充值分布数据', formats['wrap_center'])
        return current_row + 2
    all_data = rc_dist_data['all_data']
    start_row = current_row
    try:
        intervals = sorted(all_data['区间'].unique(), key=lambda x: all_data[all_data['区间']==x]['下限'].iloc[0])
    except Exception as e:
        intervals = sorted(all_data['区间'].unique())
    weeks = sorted(all_data['周'].unique())
    user_count_cols = 2 + len(intervals)
    user_perc_cols = 1 + len(intervals)
    amount_cols = 2 + len(intervals)
    amount_perc_cols = 1 + len(intervals)
    row1_cols = user_count_cols + 1 + user_perc_cols
    row2_cols = amount_cols + 1 + amount_perc_cols
    total_columns = max(row1_cols, row2_cols)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, '付费金额结构', formats['subtitle'])
    worksheet.set_row(start_row, 20)
    start_row += 1
    user_perc_offset = user_count_cols + 1
    worksheet.merge_range(start_row, 0, start_row, user_count_cols - 1, '用户数构成', formats['header'])
    worksheet.merge_range(start_row, user_perc_offset, start_row, user_perc_offset + user_perc_cols - 1, '用户占比', formats['header'])
    start_row += 1
    worksheet.write(start_row, 0, '日期', formats['header'])
    worksheet.write(start_row, 1, '全部用户', formats['header'])
    for i, interval in enumerate(intervals):
        worksheet.write(start_row, i + 2, interval, formats['header'])
        worksheet.set_column(i + 2, i + 2, 10)
    worksheet.write(start_row, user_perc_offset, '日期', formats['header'])
    for i, interval in enumerate(intervals):
        worksheet.write(start_row, user_perc_offset + i + 1, interval, formats['header'])
        worksheet.set_column(user_perc_offset + i + 1, user_perc_offset + i + 1, 10)
    start_row += 1
    data_start_row = start_row
    for week in weeks:
        week_data = all_data[all_data['周'] == week]
        if week_data.empty: continue
        row_num = data_start_row + weeks.index(week)
        worksheet.write(row_num, 0, week, formats['date'])
        total_users = week_data['用户数'].sum()
        worksheet.write(row_num, 1, total_users, formats['integer'])
        for i, interval in enumerate(intervals):
            val = week_data[week_data['区间'] == interval]['用户数'].iloc[0] if not week_data[week_data['区间'] == interval].empty else 0
            worksheet.write(row_num, i + 2, val, formats['integer'])
        worksheet.write(row_num, user_perc_offset, week, formats['date'])
        for i, interval in enumerate(intervals):
            val = week_data[week_data['区间'] == interval]['占比'].iloc[0] if not week_data[week_data['区间'] == interval].empty else 0
            worksheet.write(row_num, user_perc_offset + i + 1, val, formats['percent'])
    mean_row = data_start_row + len(weeks)
    change_row = mean_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    worksheet.write(mean_row, user_perc_offset, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, user_perc_offset, "变化率", formats['summary_text'])
    if len(weeks) >= 2:
        prev_total_users = []
        for week in weeks[:-1]:
            week_data = all_data[all_data['周'] == week]
            if not week_data.empty:
                total = week_data['用户数'].sum()
                prev_total_users.append(total)
        current_week = weeks[-1] if weeks else None
        current_total_users = None
        if current_week:
            current_week_data = all_data[all_data['周'] == current_week]
            if not current_week_data.empty:
                current_total_users = current_week_data['用户数'].sum()
        if prev_total_users:
            prev_val = sum(prev_total_users) / len(prev_total_users)
            worksheet.write(mean_row, 1, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
            if pd.notna(current_total_users) and pd.notna(prev_val) and prev_val != 0:
                change_val = ((current_total_users / prev_val) - 1) * 100
                change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                if np.isfinite(change_val):
                    worksheet.write_number(change_row, 1, change_val / 100.0, formats[change_fmt])
                else:
                    worksheet.write(change_row, 1, 'N/A', formats['summary_text'])
            else:
                worksheet.write(change_row, 1, 'N/A', formats['summary_text'])
        else:
            worksheet.write(mean_row, 1, 'N/A', formats['summary_text'])
            worksheet.write(change_row, 1, 'N/A', formats['summary_text'])
        for i, interval in enumerate(intervals):
            prev_vals = []
            for week in weeks[:-1]:
                week_data = all_data[(all_data['周'] == week) & (all_data['区间'] == interval)]
                if not week_data.empty:
                    prev_vals.append(week_data['用户数'].iloc[0])
            current_week = weeks[-1] if weeks else None
            current_val = None
            if current_week:
                current_week_data = all_data[(all_data['周'] == current_week) & (all_data['区间'] == interval)]
                if not current_week_data.empty:
                    current_val = current_week_data['用户数'].iloc[0]
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(mean_row, i + 2, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(change_row, i + 2, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(change_row, i + 2, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(change_row, i + 2, 'N/A', formats['summary_text'])
            else:
                worksheet.write(mean_row, i + 2, 'N/A', formats['summary_text'])
                worksheet.write(change_row, i + 2, 'N/A', formats['summary_text'])
        for i, interval in enumerate(intervals):
            prev_vals = []
            for week in weeks[:-1]:
                week_data = all_data[(all_data['周'] == week) & (all_data['区间'] == interval)]
                if not week_data.empty:
                    prev_vals.append(week_data['占比'].iloc[0])
            current_week = weeks[-1] if weeks else None
            current_val = None
            if current_week:
                current_week_data = all_data[(all_data['周'] == current_week) & (all_data['区间'] == interval)]
                if not current_week_data.empty:
                    current_val = current_week_data['占比'].iloc[0]
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(mean_row, user_perc_offset + i + 1, prev_val, formats['summary_percent'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(change_row, user_perc_offset + i + 1, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(change_row, user_perc_offset + i + 1, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(change_row, user_perc_offset + i + 1, 'N/A', formats['summary_text'])
            else:
                worksheet.write(mean_row, user_perc_offset + i + 1, 'N/A', formats['summary_text'])
                worksheet.write(change_row, user_perc_offset + i + 1, 'N/A', formats['summary_text'])
    start_row = change_row + 2
    amount_offset = 0
    amount_perc_offset = amount_cols + 1
    worksheet.merge_range(start_row, amount_offset, start_row, amount_offset + amount_cols - 1, '充值金额', formats['header'])
    worksheet.merge_range(start_row, amount_perc_offset, start_row, amount_perc_offset + amount_perc_cols - 1, '金额占比', formats['header'])
    start_row += 1
    worksheet.write(start_row, amount_offset, '日期', formats['header'])
    worksheet.write(start_row, amount_offset + 1, '全部金额', formats['header'])
    for i, interval in enumerate(intervals):
        worksheet.write(start_row, amount_offset + i + 2, interval, formats['header'])
        worksheet.set_column(amount_offset + i + 2, amount_offset + i + 2, 10)
    worksheet.write(start_row, amount_perc_offset, '日期', formats['header'])
    for i, interval in enumerate(intervals):
        worksheet.write(start_row, amount_perc_offset + i + 1, interval, formats['header'])
        worksheet.set_column(amount_perc_offset + i + 1, amount_perc_offset + i + 1, 10)
    start_row += 1
    amount_data_start_row = start_row
    amount_data_by_week = {}
    for week in weeks:
        week_data = all_data[all_data['周'] == week]
        if week_data.empty: continue
        amount_data = {}
        total_amount = 0
        for interval in intervals:
            interval_data = week_data[week_data['区间'] == interval]
            if not interval_data.empty:
                if '充值日志.价格总和' in interval_data.columns:
                    amount = interval_data['充值日志.价格总和'].iloc[0]
                    if not isinstance(amount, (int, float)):
                        try:
                            amount = float(amount)
                        except (ValueError, TypeError):
                            amount = 0
                else:
                    amount = 0
            else:
                amount = 0
            amount_data[interval] = amount
            total_amount += amount
        amount_data_by_week[week] = {'intervals': amount_data, 'total': total_amount}
    for week in weeks:
        if week not in amount_data_by_week: continue
        row_num = amount_data_start_row + weeks.index(week)
        week_amount_data = amount_data_by_week[week]
        worksheet.write(row_num, amount_offset, week, formats['date'])
        total_amount = week_amount_data['total']
        worksheet.write(row_num, amount_offset + 1, total_amount, formats['integer'])
        for i, interval in enumerate(intervals):
            amount = week_amount_data['intervals'].get(interval, 0)
            worksheet.write(row_num, amount_offset + i + 2, amount, formats['integer'])
        worksheet.write(row_num, amount_perc_offset, week, formats['date'])
        for i, interval in enumerate(intervals):
            amount = week_amount_data['intervals'].get(interval, 0)
            percentage = amount / total_amount if total_amount > 0 else 0
            worksheet.write(row_num, amount_perc_offset + i + 1, percentage, formats['percent'])
    amount_mean_row = amount_data_start_row + len(weeks)
    amount_change_row = amount_mean_row + 1
    worksheet.write(amount_mean_row, amount_offset, "过去4周均值", formats['summary_text'])
    worksheet.write(amount_change_row, amount_offset, "变化率", formats['summary_text'])
    worksheet.write(amount_mean_row, amount_perc_offset, "过去4周均值", formats['summary_text'])
    worksheet.write(amount_change_row, amount_perc_offset, "变化率", formats['summary_text'])
    if len(weeks) >= 2:
        prev_total_amounts = []
        for week in weeks[:-1]:
            if week in amount_data_by_week:
                total_amount = amount_data_by_week[week]['total']
                prev_total_amounts.append(total_amount)
        current_week = weeks[-1] if weeks else None
        current_total_amount = None
        if current_week and current_week in amount_data_by_week:
            current_total_amount = amount_data_by_week[current_week]['total']
        if prev_total_amounts:
            prev_val = sum(prev_total_amounts) / len(prev_total_amounts)
            worksheet.write(amount_mean_row, amount_offset + 1, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
            if pd.notna(current_total_amount) and pd.notna(prev_val) and prev_val != 0:
                change_val = ((current_total_amount / prev_val) - 1) * 100
                change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                if np.isfinite(change_val):
                    worksheet.write_number(amount_change_row, amount_offset + 1, change_val / 100.0, formats[change_fmt])
                else:
                    worksheet.write(amount_change_row, amount_offset + 1, 'N/A', formats['summary_text'])
            else:
                worksheet.write(amount_change_row, amount_offset + 1, 'N/A', formats['summary_text'])
        else:
            worksheet.write(amount_mean_row, amount_offset + 1, 'N/A', formats['summary_text'])
            worksheet.write(amount_change_row, amount_offset + 1, 'N/A', formats['summary_text'])
        for i, interval in enumerate(intervals):
            prev_vals = []
            for week in weeks[:-1]:
                if week in amount_data_by_week:
                    amount = amount_data_by_week[week]['intervals'].get(interval, 0)
                    prev_vals.append(amount)
            current_week = weeks[-1] if weeks else None
            current_val = None
            if current_week and current_week in amount_data_by_week:
                current_val = amount_data_by_week[current_week]['intervals'].get(interval, 0)
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(amount_mean_row, amount_offset + i + 2, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(amount_change_row, amount_offset + i + 2, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(amount_change_row, amount_offset + i + 2, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(amount_change_row, amount_offset + i + 2, 'N/A', formats['summary_text'])
            else:
                worksheet.write(amount_mean_row, amount_offset + i + 2, 'N/A', formats['summary_text'])
                worksheet.write(amount_change_row, amount_offset + i + 2, 'N/A', formats['summary_text'])
        for i, interval in enumerate(intervals):
            prev_vals = []
            for week in weeks[:-1]:
                if week in amount_data_by_week:
                    week_data = amount_data_by_week[week]
                    amount = week_data['intervals'].get(interval, 0)
                    total = week_data['total']
                    percentage = amount / total if total > 0 else 0
                    prev_vals.append(percentage)
            current_week = weeks[-1] if weeks else None
            current_val = None
            if current_week and current_week in amount_data_by_week:
                week_data = amount_data_by_week[current_week]
                amount = week_data['intervals'].get(interval, 0)
                total = week_data['total']
                current_val = amount / total if total > 0 else 0
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(amount_mean_row, amount_perc_offset + i + 1, prev_val, formats['summary_percent'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(amount_change_row, amount_perc_offset + i + 1, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(amount_change_row, amount_perc_offset + i + 1, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(amount_change_row, amount_perc_offset + i + 1, 'N/A', formats['summary_text'])
            else:
                worksheet.write(amount_mean_row, amount_perc_offset + i + 1, 'N/A', formats['summary_text'])
                worksheet.write(amount_change_row, amount_perc_offset + i + 1, 'N/A', formats['summary_text'])
    return amount_change_row + 2

def _write_activity_composition(worksheet, result, formats, current_row):
    activity_data = result.get('activity_analysis')
    if activity_data is None or activity_data.empty:
        total_columns = 15
        worksheet.merge_range(current_row, 0, current_row, total_columns - 1, '活动分类构成', formats['subtitle'])
        worksheet.set_row(current_row, 25)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无活动分类数据', formats['wrap_center'])
        return current_row + 2
    start_row = current_row
    headers = activity_data.columns.tolist()
    total_columns = len(headers)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, '活动分类构成', formats['subtitle'])
    worksheet.set_row(start_row, 25)
    start_row += 1
    max_col_idx = 0
    for col, header in enumerate(headers):
        worksheet.write(start_row, col, header, formats['header'])
        worksheet.set_column(col, col, 15 if header == '周' else 12)
        max_col_idx = col
    start_row += 1
    data_start_row = start_row
    for i, (_, row_data) in enumerate(activity_data.iterrows()):
        row_num = data_start_row + i
        for col_idx, col_name in enumerate(headers):
            val = row_data.get(col_name)
            if col_name == '周':
                worksheet.write(row_num, col_idx, val, formats['date'] if pd.notna(val) else formats['wrap_center'])
            else:
                worksheet.write(row_num, col_idx, val, formats['integer'] if pd.notna(val) else formats['wrap_center'])
    first_data_row_num = data_start_row
    last_data_row_num = data_start_row + len(activity_data) - 1
    first_data_col_idx = 1
    last_data_col_idx = max_col_idx
    if last_data_row_num >= first_data_row_num and last_data_col_idx >= first_data_col_idx:
        cell_range = xlsxwriter.utility.xl_range(first_data_row_num, first_data_col_idx, last_data_row_num, last_data_col_idx)
        worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    mean_row = last_data_row_num + 1
    change_row = mean_row + 1
    percentage_row = change_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    if len(activity_data) >= 2:
        for col_idx, col_name in enumerate(headers):
            if col_name == '周':
                continue
            prev_vals = activity_data[col_name].iloc[:-1].tolist()
            prev_vals = [v for v in prev_vals if pd.notna(v)]
            current_val = activity_data[col_name].iloc[-1] if not activity_data.empty else None
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(mean_row, col_idx, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(change_row, col_idx, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
            else:
                worksheet.write(mean_row, col_idx, 'N/A', formats['summary_text'])
                worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
    if not activity_data.empty:
        latest_week_data = activity_data.iloc[-1].copy()
        total_amount = 0
        for col_name in headers:
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val):
                    total_amount += val
        worksheet.write(percentage_row, 0, "本周占比", formats['summary_text'])
        for col_idx, col_name in enumerate(headers):
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val) and total_amount > 0:
                    percentage = val / total_amount
                    worksheet.write(percentage_row, col_idx, percentage, formats['percent'])
                else:
                    worksheet.write(percentage_row, col_idx, 0, formats['percent'])
        first_data_col_idx = 1
        if max_col_idx >= first_data_col_idx:
            cell_range = xlsxwriter.utility.xl_range(percentage_row, first_data_col_idx, percentage_row, max_col_idx)
            worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    return percentage_row + 2

def _write_activity2_composition(worksheet, result, formats, current_row):
    activity2_data = result.get('activity2_analysis')
    if activity2_data is None or activity2_data.empty:
        total_columns = 15
        worksheet.merge_range(current_row, 0, current_row, total_columns - 1, '活动付费构成', formats['subtitle'])
        worksheet.set_row(current_row, 25)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无活动付费数据', formats['wrap_center'])
        return current_row + 2
    start_row = current_row
    headers = activity2_data.columns.tolist()
    total_columns = len(headers)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, '活动付费构成', formats['subtitle'])
    worksheet.set_row(start_row, 25)
    start_row += 1
    max_col_idx = 0
    for col, header in enumerate(headers):
        worksheet.write(start_row, col, header, formats['header'])
        worksheet.set_column(col, col, 15 if header == '周' else 12)
        max_col_idx = col
    start_row += 1
    data_start_row = start_row
    for i, (_, row_data) in enumerate(activity2_data.iterrows()):
        row_num = data_start_row + i
        for col_idx, col_name in enumerate(headers):
            val = row_data.get(col_name)
            if col_name == '周':
                worksheet.write(row_num, col_idx, val, formats['date'] if pd.notna(val) else formats['wrap_center'])
            else:
                worksheet.write(row_num, col_idx, val, formats['integer'] if pd.notna(val) else formats['wrap_center'])
    first_data_row_num = data_start_row
    last_data_row_num = data_start_row + len(activity2_data) - 1
    first_data_col_idx = 1
    last_data_col_idx = max_col_idx
    if last_data_row_num >= first_data_row_num and last_data_col_idx >= first_data_col_idx:
        cell_range = xlsxwriter.utility.xl_range(first_data_row_num, first_data_col_idx, last_data_row_num, last_data_col_idx)
        worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    mean_row = last_data_row_num + 1
    change_row = mean_row + 1
    percentage_row = change_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    if len(activity2_data) >= 2:
        for col_idx, col_name in enumerate(headers):
            if col_name == '周':
                continue
            prev_vals = activity2_data[col_name].iloc[:-1].tolist()
            prev_vals = [v for v in prev_vals if pd.notna(v)]
            current_val = activity2_data[col_name].iloc[-1] if not activity2_data.empty else None
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(mean_row, col_idx, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(change_row, col_idx, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
            else:
                worksheet.write(mean_row, col_idx, 'N/A', formats['summary_text'])
                worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
    if not activity2_data.empty:
        latest_week_data = activity2_data.iloc[-1].copy()
        total_amount = 0
        for col_name in headers:
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val):
                    total_amount += val
        worksheet.write(percentage_row, 0, "本周占比", formats['summary_text'])
        for col_idx, col_name in enumerate(headers):
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val) and total_amount > 0:
                    percentage = val / total_amount
                    worksheet.write(percentage_row, col_idx, percentage, formats['percent'])
                else:
                    worksheet.write(percentage_row, col_idx, 0, formats['percent'])
        first_data_col_idx = 1
        if max_col_idx >= first_data_col_idx:
            cell_range = xlsxwriter.utility.xl_range(percentage_row, first_data_col_idx, percentage_row, max_col_idx)
            worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    return percentage_row + 2

def _write_top20_data(worksheet, result, formats, current_row):
    top20_data = result.get('top20_analysis')
    if top20_data is None or top20_data.empty:
        worksheet.merge_range(current_row, 0, current_row, 3, 'TOP 用户', formats['subtitle'])
        worksheet.set_row(current_row, 25)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无TOP用户数据', formats['wrap_center'])
        return current_row + 2
    sorted_date_columns = []
    if hasattr(top20_data, 'attrs') and 'sorted_date_columns' in top20_data.attrs:
        sorted_date_columns = top20_data.attrs['sorted_date_columns']
    else:
        date_columns = [col for col in top20_data.columns if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日', str(col))]
        if date_columns:
            try:
                date_objects = []
                for col in date_columns:
                    try:
                        date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(col))
                        if date_match:
                            date_str = date_match.group(1)
                            date_obj = pd.to_datetime(date_str, errors='coerce')
                            if not pd.isna(date_obj):
                                date_objects.append((col, date_obj))
                        else:
                            date_obj = pd.to_datetime(col, errors='coerce')
                            if not pd.isna(date_obj):
                                date_objects.append((col, date_obj))
                    except Exception as e:
                        pass
                date_objects.sort(key=lambda x: x[1])
                sorted_date_columns = [item[0] for item in date_objects]
            except Exception as e:
                sorted_date_columns = date_columns
    start_row = current_row
    base_headers = ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额', '阶段汇总']
    total_columns = len(base_headers) + len(sorted_date_columns)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, 'TOP 用户', formats['subtitle'])
    worksheet.set_row(start_row, 25)
    start_row += 1
    for header in base_headers:
        if header not in top20_data.columns:
            top20_data[header] = ""
    header_format = formats['header']
    for col, header in enumerate(base_headers):
        worksheet.write(start_row, col, header, header_format)
        if header == '角色ID':
            worksheet.set_column(col, col, 15)
        elif header == '角色名':
            worksheet.set_column(col, col, 15)
        elif header == '创角时间':
            worksheet.set_column(col, col, 18)
        elif header == '最后登录时间':
            worksheet.set_column(col, col, 18)
        elif header == '累计充值金额':
            worksheet.set_column(col, col, 15)
        elif header == '阶段汇总':
            worksheet.set_column(col, col, 15)
    for i, date_col in enumerate(sorted_date_columns):
        col_idx = len(base_headers) + i
        try:
            date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(date_col))
            if date_match:
                date_str = date_match.group(1)
                date_obj = pd.to_datetime(date_str, errors='coerce')
                if not pd.isna(date_obj):
                    display_header = f"{date_obj.strftime('%Y-%m-%d')}"
                    worksheet.write(start_row, col_idx, display_header, header_format)
                else:
                    worksheet.write(start_row, col_idx, date_col, header_format)
            else:
                worksheet.write(start_row, col_idx, date_col, header_format)
            worksheet.set_column(col_idx, col_idx, 15)
        except Exception as e:
            worksheet.write(start_row, col_idx, date_col, header_format)
            worksheet.set_column(col_idx, col_idx, 15)
    start_row += 1
    try:
        # 优先按阶段汇总排序，如果没有阶段汇总列则按累计充值金额排序
        if '阶段汇总' in top20_data.columns:
            top20_data = top20_data.sort_values(by=['阶段汇总'], ascending=False)
        else:
            top20_data = top20_data.sort_values(by=['累计充值金额'], ascending=False)
    except Exception as e:
        pass
    try:
        top20_data['角色ID'] = top20_data['角色ID'].astype(float)
    except Exception as e:
        pass
    data_start_row = start_row
    for i, (_, row_data) in enumerate(top20_data.iterrows()):
        row_num = data_start_row + i
        for col_idx, col_name in enumerate(base_headers):
            val = row_data.get(col_name)
            if col_name == '累计充值金额' or col_name == '阶段汇总':
                worksheet.write(row_num, col_idx, val, formats['top20_integer'] if pd.notna(val) else formats['top20_cell'])
            elif col_name == '角色ID':
                worksheet.write(row_num, col_idx, val, formats['top20_cell'] if pd.notna(val) else formats['top20_cell'])
            else:
                worksheet.write(row_num, col_idx, val, formats['top20_cell'] if pd.notna(val) else formats['top20_cell'])
        for i, date_col in enumerate(sorted_date_columns):
            col_idx = len(base_headers) + i
            val = row_data.get(date_col, 0)
            worksheet.write(row_num, col_idx, val, formats['top20_integer'] if pd.notna(val) and val > 0 else formats['top20_cell'])
    last_data_row_num = data_start_row + len(top20_data) - 1
    first_data_row_num = data_start_row
    first_data_col_idx = len(base_headers)
    last_data_col_idx = len(base_headers) + len(sorted_date_columns) - 1
    if last_data_row_num >= first_data_row_num and last_data_col_idx >= first_data_col_idx:
        cell_range = xlsxwriter.utility.xl_range(first_data_row_num, first_data_col_idx, last_data_row_num, last_data_col_idx)
        worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    return last_data_row_num + 2

def _write_new_top_data(worksheet, result, formats, current_row):
    new_top_data = result.get('new_top_analysis')
    if new_top_data is None or new_top_data.empty:
        worksheet.merge_range(current_row, 0, current_row, 3, '新注册用户TOP', formats['subtitle'])
        worksheet.set_row(current_row, 25)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无新注册用户TOP数据', formats['wrap_center'])
        return current_row + 2
    sorted_date_columns = []
    if hasattr(new_top_data, 'attrs') and 'sorted_date_columns' in new_top_data.attrs:
        sorted_date_columns = new_top_data.attrs['sorted_date_columns']
    else:
        date_columns = [col for col in new_top_data.columns if re.match(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{4}年\d{1,2}月\d{1,2}日', str(col))]
        if date_columns:
            try:
                date_objects = []
                for col in date_columns:
                    try:
                        date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(col))
                        if date_match:
                            date_str = date_match.group(1)
                            date_obj = pd.to_datetime(date_str, errors='coerce')
                            if not pd.isna(date_obj):
                                date_objects.append((col, date_obj))
                        else:
                            date_obj = pd.to_datetime(col, errors='coerce')
                            if not pd.isna(date_obj):
                                date_objects.append((col, date_obj))
                    except Exception as e:
                        pass
                date_objects.sort(key=lambda x: x[1])
                sorted_date_columns = [item[0] for item in date_objects]
            except Exception as e:
                sorted_date_columns = date_columns
    start_row = current_row
    base_headers = ['角色ID', '角色名', '创角时间', '最后登录时间', '累计充值金额']
    total_columns = len(base_headers) + len(sorted_date_columns)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, '新注册用户TOP', formats['subtitle'])
    worksheet.set_row(start_row, 25)
    start_row += 1
    for header in base_headers:
        if header not in new_top_data.columns:
            new_top_data[header] = ""
    header_format = formats['header']
    for col, header in enumerate(base_headers):
        worksheet.write(start_row, col, header, header_format)
        if header == '角色ID':
            worksheet.set_column(col, col, 15)
        elif header == '角色名':
            worksheet.set_column(col, col, 15)
        elif header == '创角时间':
            worksheet.set_column(col, col, 18)
        elif header == '最后登录时间':
            worksheet.set_column(col, col, 18)
        elif header == '累计充值金额':
            worksheet.set_column(col, col, 15)
        elif header == '阶段汇总':
            worksheet.set_column(col, col, 15)
    for i, date_col in enumerate(sorted_date_columns):
        col_idx = len(base_headers) + i
        try:
            date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', str(date_col))
            if date_match:
                date_str = date_match.group(1)
                date_obj = pd.to_datetime(date_str, errors='coerce')
                if not pd.isna(date_obj):
                    display_header = f"{date_obj.strftime('%Y-%m-%d')}"
                    worksheet.write(start_row, col_idx, display_header, header_format)
                else:
                    worksheet.write(start_row, col_idx, date_col, header_format)
            else:
                worksheet.write(start_row, col_idx, date_col, header_format)
            worksheet.set_column(col_idx, col_idx, 15)
        except Exception as e:
            worksheet.write(start_row, col_idx, date_col, header_format)
            worksheet.set_column(col_idx, col_idx, 15)
    start_row += 1
    try:
        new_top_data = new_top_data.sort_values(by=['累计充值金额'], ascending=False)
    except Exception as e:
        pass
    try:
        new_top_data['角色ID'] = new_top_data['角色ID'].astype(float)
    except Exception as e:
        pass
    data_start_row = start_row
    for i, (_, row_data) in enumerate(new_top_data.iterrows()):
        row_num = data_start_row + i
        for col_idx, col_name in enumerate(base_headers):
            val = row_data.get(col_name)
            if col_name == '累计充值金额' or col_name == '阶段汇总':
                worksheet.write(row_num, col_idx, val, formats['top20_integer'] if pd.notna(val) else formats['top20_cell'])
            elif col_name == '角色ID':
                worksheet.write(row_num, col_idx, val, formats['top20_cell'] if pd.notna(val) else formats['top20_cell'])
            else:
                worksheet.write(row_num, col_idx, val, formats['top20_cell'] if pd.notna(val) else formats['top20_cell'])
        for i, date_col in enumerate(sorted_date_columns):
            col_idx = len(base_headers) + i
            val = row_data.get(date_col, 0)
            worksheet.write(row_num, col_idx, val, formats['top20_integer'] if pd.notna(val) and val > 0 else formats['top20_cell'])
    last_data_row_num = data_start_row + len(new_top_data) - 1
    first_data_row_num = data_start_row
    first_data_col_idx = len(base_headers)
    last_data_col_idx = len(base_headers) + len(sorted_date_columns) - 1
    if last_data_row_num >= first_data_row_num and last_data_col_idx >= first_data_col_idx:
        cell_range = xlsxwriter.utility.xl_range(first_data_row_num, first_data_col_idx, last_data_row_num, last_data_col_idx)
        worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    return last_data_row_num + 2

def _write_cultivation_lines(worksheet, result, formats, current_row):
    cultivation_data = result.get('cultivation_analysis')
    if cultivation_data is None or cultivation_data.empty:
        total_columns = 15
        worksheet.merge_range(current_row, 0, current_row, total_columns - 1, '养成线数据分析', formats['subtitle'])
        worksheet.set_row(current_row, 20)
        current_row += 1
        worksheet.merge_range(f'A{current_row+1}:D{current_row+1}', '无养成线数据', formats['wrap_center'])
        return current_row + 2
    start_row = current_row
    headers = cultivation_data.columns.tolist()
    total_columns = len(headers)
    worksheet.merge_range(start_row, 0, start_row, total_columns - 1, '养成线数据分析', formats['subtitle'])
    worksheet.set_row(start_row, 20)
    start_row += 1
    max_col_idx = 0
    for col, header in enumerate(headers):
        worksheet.write(start_row, col, header, formats['header'])
        worksheet.set_column(col, col, 15 if header == '周' else 12)
        max_col_idx = col
    start_row += 1
    data_start_row = start_row
    for i, (_, row_data) in enumerate(cultivation_data.iterrows()):
        row_num = data_start_row + i
        for col_idx, col_name in enumerate(headers):
            val = row_data.get(col_name)
            if col_name == '周':
                worksheet.write(row_num, col_idx, val, formats['date'] if pd.notna(val) else formats['wrap_center'])
            else:
                worksheet.write(row_num, col_idx, val, formats['integer'] if pd.notna(val) else formats['wrap_center'])
    first_data_row_num = data_start_row
    last_data_row_num = data_start_row + len(cultivation_data) - 1
    first_data_col_idx = 1
    last_data_col_idx = max_col_idx
    if last_data_row_num >= first_data_row_num and last_data_col_idx >= first_data_col_idx:
        cell_range = xlsxwriter.utility.xl_range(first_data_row_num, first_data_col_idx, last_data_row_num, last_data_col_idx)
        worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    mean_row = last_data_row_num + 1
    change_row = mean_row + 1
    percentage_row = change_row + 1
    worksheet.write(mean_row, 0, "过去4周均值", formats['summary_text'])
    worksheet.write(change_row, 0, "变化率", formats['summary_text'])
    if len(cultivation_data) >= 2:
        for col_idx, col_name in enumerate(headers):
            if col_name == '周':
                continue
            prev_vals = cultivation_data[col_name].iloc[:-1].tolist()
            prev_vals = [v for v in prev_vals if pd.notna(v)]
            current_val = cultivation_data[col_name].iloc[-1] if not cultivation_data.empty else None
            if prev_vals:
                prev_val = sum(prev_vals) / len(prev_vals)
                worksheet.write(mean_row, col_idx, prev_val, formats['summary_integer'] if pd.notna(prev_val) else formats['summary_text'])
                if pd.notna(current_val) and pd.notna(prev_val) and prev_val != 0:
                    change_val = ((current_val / prev_val) - 1) * 100
                    change_fmt = 'summary_change_pos' if change_val >= 0 else 'summary_change_neg'
                    if np.isfinite(change_val):
                        worksheet.write_number(change_row, col_idx, change_val / 100.0, formats[change_fmt])
                    else:
                        worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
                else:
                    worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
            else:
                worksheet.write(mean_row, col_idx, 'N/A', formats['summary_text'])
                worksheet.write(change_row, col_idx, 'N/A', formats['summary_text'])
    if not cultivation_data.empty:
        latest_week_data = cultivation_data.iloc[-1].copy()
        total_amount = 0
        for col_name in headers:
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val):
                    total_amount += val
        worksheet.write(percentage_row, 0, "本周占比", formats['summary_text'])
        for col_idx, col_name in enumerate(headers):
            if col_name != '周':
                val = latest_week_data.get(col_name)
                if pd.notna(val) and total_amount > 0:
                    percentage = val / total_amount
                    worksheet.write(percentage_row, col_idx, percentage, formats['percent'])
                else:
                    worksheet.write(percentage_row, col_idx, 0, formats['percent'])
        first_data_col_idx = 1
        if max_col_idx >= first_data_col_idx:
            cell_range = xlsxwriter.utility.xl_range(percentage_row, first_data_col_idx, percentage_row, max_col_idx)
            worksheet.conditional_format(cell_range, {'type': 'data_bar', 'bar_color': '#FFD966'})
    return percentage_row + 2

def export_to_excel(analysis_results, filename=None):
    if filename is None:
        filename = OUTPUT_CONFIG.get("EXCEL_FILENAME", "渠道周数据分析报告_default.xlsx")
    output_dir = os.path.dirname(filename)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
        except OSError as e:
            print(f"错误: 无法创建输出目录 {output_dir}: {e}")
            filename = os.path.basename(filename)
    try:
        with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
            workbook = writer.book
            workbook.nan_inf_to_errors = True
            formats = _define_excel_formats(workbook)
            summary_results = {}
            other_results = {}
            for channel_name, result in analysis_results.items():
                if result is None or not isinstance(result, dict):
                    continue
                if channel_name.startswith('D版汇总') or channel_name.startswith('E版汇总'):
                    summary_results[channel_name] = result
                else:
                    other_results[channel_name] = result
            ordered_results = {**summary_results, **other_results}
            processed_channels = 0
            for channel_name, result in ordered_results.items():
                display_name = channel_name
                if channel_name.startswith('D-'): display_name = f"{channel_name[2:]}(D版本)"
                elif channel_name.startswith('E-'): display_name = f"{channel_name[2:]}(E版本)"
                safe_sheet_name = re.sub(r'[\\/*?:\[\]]', '_', display_name)[:31]
                worksheet = workbook.add_worksheet(safe_sheet_name)
                formats = _define_excel_formats(workbook)
                default_width = 14
                for i in range(15):
                    worksheet.set_column(i, i, default_width)
                current_row = 0
                current_row = _write_title_and_summary(worksheet, result, formats, current_row)
                current_row = _write_anomalies(worksheet, result, formats, current_row)
                current_row = _write_basic_data_comparison(worksheet, result, formats, current_row)
                current_row = _write_payment_tiers(worksheet, result, formats, current_row)
                current_row = _write_recharge_distribution(worksheet, result, formats, current_row)
                current_row = _write_activity_composition(worksheet, result, formats, current_row)
                current_row = _write_activity2_composition(worksheet, result, formats, current_row)
                current_row = _write_cultivation_lines(worksheet, result, formats, current_row)
                current_row = _write_top20_data(worksheet, result, formats, current_row)
                current_row = _write_new_top_data(worksheet, result, formats, current_row)
                default_width = 14
                for i in range(15):
                    worksheet.set_column(i, i, default_width)
            return filename
    except ImportError as e:
         print(f"❌ 错误: 缺少必要的库: {e}")
         print("   请确保 'xlsxwriter' 库已安装: pip install xlsxwriter")
         return None
    except Exception as e:
        print(f"❌ 写入Excel文件时发生未知错误: {e}")
        print(traceback.format_exc())
        return None

def get_latest_dau_from_api(channel_name, version):
    real_channel_name = channel_name.replace('D-', '').replace('E-', '')
    api_version = version.lower()[0]
    data_dir = "data"
    pattern = os.path.join(data_dir, f"{api_version.upper()}_AVERAGE_*.csv")
    api_files = glob.glob(pattern)
    if not api_files:
        return 0
    api_file_path = max(api_files, key=os.path.getmtime)
    try:
        df = pd.read_csv(api_file_path)
        date_col = None
        channel_col = None
        dau_col = None
        for col in df.columns:
            if col in ['日期', '时间', 'date', 'time']:
                date_col = col
                break
        for col in df.columns:
            if col in ['渠道', '创角渠道名称', 'channel']:
                channel_col = col
                break
        for col in df.columns:
            if 'dau' in str(col).lower() or '日活' in str(col).lower():
                dau_col = col
                break
        if date_col is None and len(df.columns) >= 1:
            date_col = df.columns[0]
        if channel_col is None and len(df.columns) >= 2 and 'DAU' not in df.columns[1]:
            channel_col = df.columns[1]
        elif channel_col is None and 'DAU' in df.columns:
            df['virtual_channel'] = '所有渠道'
            channel_col = 'virtual_channel'
        if dau_col is None and len(df.columns) >= 3:
            dau_col = df.columns[2]
        if date_col is None or dau_col is None:
            print(f"错误: 无法识别必要的日期列或DAU列")
            return 0
        rename_dict = {}
        if date_col != '日期':
            rename_dict[date_col] = '日期'
        if channel_col is not None and channel_col != '渠道':
            rename_dict[channel_col] = '渠道'
        if dau_col != 'DAU':
            rename_dict[dau_col] = 'DAU'
        if rename_dict:
            df = df.rename(columns=rename_dict)
        if '日期' in df.columns and df['日期'].dtype == 'object':
            df = df[df['日期'] != '阶段汇总'].copy()
        df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
        df.dropna(subset=['日期'], inplace=True)
        if 'DAU' in df.columns:
            df['DAU'] = pd.to_numeric(df['DAU'], errors='coerce').fillna(0)
        if '渠道' in df.columns:
            df['渠道'] = df['渠道'].astype(str)
            try:
                if '汇总' in channel_name:
                    df_filtered = df.copy()
                else:
                    channel_filter = df['渠道'].str.contains(real_channel_name, case=False, na=False)
                    if not channel_filter.any():
                        return 0
                    df_filtered = df[channel_filter].copy()
            except Exception as e:
                return 0
        else:
            if '汇总' in channel_name:
                df_filtered = df.copy()
            else:
                return 0
        if df_filtered.empty:
            return 0
        df_filtered['周'] = df_filtered['日期'].dt.to_period('W-MON').apply(lambda p: p.start_time)
        weekly_data = df_filtered.groupby('周').agg({'DAU': 'mean'}).reset_index()
        weekly_data = weekly_data.rename(columns={'周': '日期'})
        weekly_data = weekly_data.sort_values('日期').tail(5).reset_index(drop=True)
        if weekly_data.empty:
            return 0
        latest_dau = weekly_data.sort_values('日期', ascending=False).iloc[0]['DAU']
        return latest_dau
    except Exception as e:
        print(f"加载API均值数据出错: {e}")
        print(traceback.format_exc())
        return 0

def load_api_average_data(channel_name, explicit_version=None):
    real_channel_name = channel_name.replace('D-', '').replace('E-', '')
    api_version = "d"
    if explicit_version:
        api_version = explicit_version.lower()[0]
    elif channel_name.startswith('E-') or "E版本" in channel_name or "E 汇总" in channel_name:
        api_version = "e"
    data_dir = "data"
    pattern = os.path.join(data_dir, f"{api_version.upper()}_AVERAGE_*.csv")
    api_files = glob.glob(pattern)
    if not api_files:
        return None
    api_file_path = max(api_files, key=os.path.getmtime)
    try:
        df = pd.read_csv(api_file_path)
        if len(df.columns) < 3:
            print(f"错误: API均值CSV文件列数太少 ({len(df.columns)}): {api_file_path}")
            return None
        date_col = None
        channel_col = None
        dau_col = None
        rate_col = None
        for col in df.columns:
            if col in ['日期', '时间', 'date', 'time']:
                date_col = col
                break
        for col in df.columns:
            if col in ['渠道', '创角渠道名称', 'channel']:
                channel_col = col
                break
        if channel_col is None and 'DAU' in df.columns:
            pass
        for col in df.columns:
            if 'dau' in str(col).lower() or '日活' in str(col).lower():
                dau_col = col
                break
        for col in df.columns:
            if '付费率' in str(col) or 'rate' in str(col).lower():
                rate_col = col
                break
        if date_col is None and len(df.columns) >= 1:
            date_col = df.columns[0]
        if channel_col is None and len(df.columns) >= 2 and 'DAU' not in df.columns[1]:
            channel_col = df.columns[1]
        elif channel_col is None and 'DAU' in df.columns:
            df['virtual_channel'] = '所有渠道'
            channel_col = 'virtual_channel'
        if dau_col is None and len(df.columns) >= 3:
            dau_col = df.columns[2]
        if rate_col is None and len(df.columns) >= 4:
            rate_col = df.columns[3]
        elif rate_col is None and dau_col is not None:
            for col in df.columns:
                if col != date_col and col != channel_col and col != dau_col:
                    rate_col = col
                    break
        if date_col is None or dau_col is None:
            print(f"错误: 无法识别必要的日期列或DAU列")
            return None
        rename_dict = {}
        if date_col != '日期':
            rename_dict[date_col] = '日期'
        if channel_col is not None and channel_col != '渠道':
            rename_dict[channel_col] = '渠道'
        if dau_col != 'DAU':
            rename_dict[dau_col] = 'DAU'
        if rate_col is not None and rate_col != '付费率':
            rename_dict[rate_col] = '付费率'
        if rename_dict:
            df = df.rename(columns=rename_dict)
        if '日期' in df.columns and df['日期'].dtype == 'object':
            df = df[df['日期'] != '阶段汇总'].copy()
        df['日期'] = pd.to_datetime(df['日期'], errors='coerce')
        df.dropna(subset=['日期'], inplace=True)
        if 'DAU' in df.columns:
            df['DAU'] = pd.to_numeric(df['DAU'], errors='coerce').fillna(0)
        if '付费率' in df.columns:
            if pd.api.types.is_numeric_dtype(df['付费率']):
                if (df['付费率'] > 1).any():
                    df['付费率'] = df['付费率'] / 100.0
            else:
                df['付费率'] = _convert_percentage_column(df['付费率'])
        if '渠道' in df.columns:
            df['渠道'] = df['渠道'].astype(str)
            try:
                if '汇总' in channel_name:
                    df_filtered = df.copy()
                else:
                    channel_filter = df['渠道'].str.contains(real_channel_name, case=False, na=False)
                    if not channel_filter.any():
                        return None
                    df_filtered = df[channel_filter].copy()
            except Exception as e:
                return None
        else:
            if '汇总' in channel_name:
                df_filtered = df.copy()
            else:
                return None
        if df_filtered.empty:
            return None
        agg_dict = {}
        if 'DAU' in df_filtered.columns:
            agg_dict['DAU'] = 'mean'
        if '付费率' in df_filtered.columns:
            agg_dict['付费率'] = 'mean'
        if not agg_dict:
            print("错误: 没有可聚合的指标列")
            return None
        df_filtered = df_filtered.sort_values('日期')
        start_date = df_filtered['日期'].min()
        df_filtered['天数'] = (df_filtered['日期'] - start_date).dt.days
        df_filtered['周期'] = df_filtered['天数'] // 7
        df_filtered['周起始日'] = start_date + pd.to_timedelta(df_filtered['周期'] * 7, unit='D')
        weekly_data = df_filtered.groupby('周起始日').agg(agg_dict).reset_index()
        weekly_data = weekly_data.rename(columns={'周起始日': '日期'})
        weekly_data = weekly_data.sort_values('日期').tail(5).reset_index(drop=True)
        metrics = list(agg_dict.keys())
        return {
            "weekly_data": weekly_data.to_dict('records'),
            "metrics": metrics
        }
    except Exception as e:
        print(f"加载API均值数据出错: {e}")
        print(traceback.format_exc())
        return None

def ai_analysis_task(task_info):
    channel_name = task_info['channel_name']
    metrics_total = task_info['metrics_total']
    anomalies = task_info['anomalies']
    last_7_days = task_info['last_7_days']
    previous_7_days = task_info['previous_7_days']
    new_user_data = task_info['new_user_data']
    recharge_distribution = task_info['recharge_distribution']
    activity_analysis = task_info['activity_analysis']
    activity2_analysis = task_info['activity2_analysis']
    cultivation_analysis = task_info['cultivation_analysis']
    top20_analysis = task_info['top20_analysis']
    new_top_analysis = task_info['new_top_analysis']
    analysis_text = analyze_with_ai(
        channel_name, metrics_total, anomalies,
        last_7_days, previous_7_days, new_user_data,
        recharge_distribution, activity_analysis,
        activity2_analysis, cultivation_analysis,
        top20_analysis, new_top_analysis,
        raw_data_context=task_info.get('raw_data_context', None)
    )
    return channel_name, analysis_text

def main(csv_files_abs=None, excel_filename_abs=None, analysis_date=None, existing_results=None):
    # 获取当前AI配置
    current_config = get_current_ai_config()
    
    analysis_date = analysis_date or datetime.now().date()
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip().lower()
    is_all_channels_mode = (target_channel_config == "all")
    file_paths = {}
    versions = ['D', 'E']
    data_types = ['VERSION', 'RC_DIST', 'ACTIVITY', 'ACTIVITY2', 'GROWTH', 'CULTIVATION', 'AVERAGE', 'TOP20', 'NEW_TOP']
    if csv_files_abs:
        for v in versions:
            for dt in data_types:
                key = f"{v}_{dt}"
                if dt == 'GROWTH' and key not in csv_files_abs and f"{v}_CULTIVATION" in csv_files_abs:
                    file_paths[key] = csv_files_abs.get(f"{v}_CULTIVATION")
                else:
                    file_paths[key] = csv_files_abs.get(key)
    else:
        data_dir = "data"
        import glob
        def find_latest_file(pattern):
            files = glob.glob(os.path.join(data_dir, pattern))
            return max(files, key=os.path.getmtime) if files else None
        for v in versions:
            file_paths[f'{v}_VERSION'] = find_latest_file(f"{v}_VERSION_*.csv") or find_latest_file(f"{v}版本_*.csv")
            file_paths[f'{v}_RC_DIST'] = find_latest_file(f"{v}_RC_DIST_*.csv")
            file_paths[f'{v}_ACTIVITY'] = find_latest_file(f"{v}_ACTIVITY_*.csv")
            file_paths[f'{v}_ACTIVITY2'] = find_latest_file(f"{v}_ACTIVITY2_*.csv")
            file_paths[f'{v}_GROWTH'] = find_latest_file(f"{v}_GROWTH_*.csv") or find_latest_file(f"{v}_CULTIVATION_*.csv")
            file_paths[f'{v}_AVERAGE'] = find_latest_file(f"{v}_AVERAGE_*.csv")
            file_paths[f'{v}_TOP20'] = find_latest_file(f"{v}_TOP20_*.csv")
            file_paths[f'{v}_NEW_TOP'] = find_latest_file(f"{v}_NEW_TOP_*.csv")
    excel_output_file = excel_filename_abs or OUTPUT_CONFIG.get("EXCEL_FILENAME", "渠道周数据分析报告_default.xlsx")
    analysis_results_final = {}
    loaded_data = {}
    versions_with_data = []
    for version in versions:
        version_df_path = file_paths.get(f'{version}_VERSION')
        if not version_df_path:
            continue
        version_df = load_data(version_df_path)
        if version_df is None or version_df.empty:
            continue
        versions_with_data.append(version)
        loaded_data[f'{version}_VERSION'] = version_df
        rc_dist_df_path = file_paths.get(f'{version}_RC_DIST')
        activity_df_path = file_paths.get(f'{version}_ACTIVITY')
        activity2_df_path = file_paths.get(f'{version}_ACTIVITY2')
        cultivation_df_path = file_paths.get(f'{version}_GROWTH')
        top20_df_path = file_paths.get(f'{version}_TOP20')
        new_top_df_path = file_paths.get(f'{version}_NEW_TOP')
        loaded_data[f'{version}_RC_DIST'] = load_data(rc_dist_df_path) if rc_dist_df_path else None
        loaded_data[f'{version}_ACTIVITY'] = load_data(activity_df_path) if activity_df_path else None
        loaded_data[f'{version}_ACTIVITY2'] = load_data(activity2_df_path) if activity2_df_path else None
        loaded_data[f'{version}_GROWTH'] = load_data(cultivation_df_path) if cultivation_df_path else None
        loaded_data[f'{version}_TOP20'] = load_data(top20_df_path) if top20_df_path else None
        loaded_data[f'{version}_NEW_TOP'] = load_data(new_top_df_path) if new_top_df_path else None
    if not versions_with_data:
        print("\n错误: 所有版本的数据文件都不可用或为空。")
        return {}
    for version in versions_with_data:
        version_df = loaded_data[f'{version}_VERSION']
        rc_dist_df = loaded_data[f'{version}_RC_DIST']
        activity_df = loaded_data[f'{version}_ACTIVITY']
        activity2_df = loaded_data[f'{version}_ACTIVITY2']
        cultivation_df = loaded_data[f'{version}_GROWTH']
        top20_df = loaded_data[f'{version}_TOP20']
        new_top_df = loaded_data[f'{version}_NEW_TOP']
        if is_all_channels_mode:
            user_type = "NEW" if '_NEW_' in os.path.basename(version_df_path) else "OLD"
            agg_df = aggregate_channel_data(version_df, version, user_type)
            if agg_df is None or agg_df.empty:
                 continue
            channel_name_agg = f"{version} 汇总"
            result = analyze_channel_data(agg_df, channel_name_agg, version=version)
            if result is not None:
                result['recharge_distribution'] = analyze_recharge_distribution(rc_dist_df, channel_name_agg) if rc_dist_df is not None else None
                result['activity_analysis'] = analyze_activity_data(activity_df, channel_name_agg) if activity_df is not None else None
                result['activity2_analysis'] = analyze_activity2_data(activity2_df, channel_name_agg) if activity2_df is not None else None
                result['cultivation_analysis'] = analyze_cultivation_data(cultivation_df, channel_name_agg) if cultivation_df is not None else None
                result['top20_analysis'] = analyze_top20_data(top20_df, channel_name_agg) if top20_df is not None else None
                result['new_top_analysis'] = analyze_new_top_data(new_top_df, channel_name_agg) if new_top_df is not None else None
                # 构建原始数据上下文，确保AI获得与Excel相同的完整数据
                raw_data_context = {
                    'original_dataframe_info': {
                        'shape': agg_df.shape if agg_df is not None else (0, 0),
                        'columns': agg_df.columns.tolist() if agg_df is not None else [],
                        'sample_data': agg_df.head(5).to_dict('records') if agg_df is not None and not agg_df.empty else []
                    },
                    # 使用经过分析处理的数据，而不是原始完整数据，以控制token数量
                    'recharge_distribution_raw': result['recharge_distribution']['all_data'].to_dict('records') if result['recharge_distribution'] and 'all_data' in result['recharge_distribution'] and not result['recharge_distribution']['all_data'].empty else [],
                    'activity_raw': result['activity_analysis'].to_dict('records') if result['activity_analysis'] is not None and not result['activity_analysis'].empty else [],
                    'activity2_raw': result['activity2_analysis'].to_dict('records') if result['activity2_analysis'] is not None and not result['activity2_analysis'].empty else [],
                    'cultivation_raw': result['cultivation_analysis'].to_dict('records') if result['cultivation_analysis'] is not None and not result['cultivation_analysis'].empty else [],
                    'top20_raw': result['top20_analysis'].to_dict('records') if result['top20_analysis'] is not None and not result['top20_analysis'].empty else [],
                    'new_top_raw': result['new_top_analysis'].to_dict('records') if result['new_top_analysis'] is not None and not result['new_top_analysis'].empty else [],
                    'version_info': version,
                    'analysis_mode': 'aggregated'
                }
                
                ai_task = {
                    'channel_name': channel_name_agg,
                    'metrics_total': result['metrics_total'],
                    'anomalies': result['anomalies'],
                    'last_7_days': result['last_7_days'],
                    'previous_7_days': result['previous_7_days'],
                    'new_user_data': None,
                    'recharge_distribution': result['recharge_distribution'],
                    'activity_analysis': result['activity_analysis'],
                    'activity2_analysis': result['activity2_analysis'],
                    'cultivation_analysis': result['cultivation_analysis'],
                    'top20_analysis': result['top20_analysis'],
                    'new_top_analysis': result['new_top_analysis'],
                    'raw_data_context': raw_data_context,
                    'result_key': channel_name_agg
                }
                try:
                    channel_name, analysis_text = ai_analysis_task(ai_task)
                    result['analysis_text'] = analysis_text
                except Exception as e:
                    print(f"\u274c 渠道 {channel_name_agg} 的AI分析失败: {e}")
                    traceback.print_exc()
                    result['analysis_text'] = f"渠道 {channel_name_agg} 的AI分析失败，请查看日志了解详情。"
                analysis_results_final[channel_name_agg] = result
            else:
                pass
            channel_data_map = separate_data_by_channel(version_df)
            if not channel_data_map:
                continue
            target_channel_orig = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "")
            if target_channel_orig.lower() != "all" and ',' in target_channel_orig:
                channel_list = [ch.strip() for ch in target_channel_orig.split(',') if ch.strip()]
                if len(channel_list) > 1:
                    pass
            min_dau_threshold = CHANNEL_FILTER_CONFIG.get("MIN_DAU_THRESHOLD", 0)
            if min_dau_threshold > 0:
                filtered_channels = {}
                for name, df_ch in channel_data_map.items():
                    latest_dau = get_latest_dau_from_api(name, version)
                    if latest_dau >= min_dau_threshold:
                        filtered_channels[name] = df_ch
                    else:
                        pass
                if filtered_channels:
                    pass
                channels_to_process = filtered_channels
            else:
                channels_to_process = channel_data_map
            channel_results = {}
            ai_analysis_tasks = []
            for channel_name, channel_df in channels_to_process.items():
                result = analyze_channel_data(channel_df, channel_name, version=version)
                if result is None:
                    continue
                current_rc_dist = rc_dist_df[rc_dist_df['创角渠道名称'] == channel_name] if rc_dist_df is not None and '创角渠道名称' in rc_dist_df else None
                current_activity = activity_df[activity_df['创角渠道名称'] == channel_name] if activity_df is not None and '创角渠道名称' in activity_df else None
                current_activity2 = activity2_df[activity2_df['创角渠道名称'] == channel_name] if activity2_df is not None and '创角渠道名称' in activity2_df else None
                current_cultivation = cultivation_df[cultivation_df['创角渠道名称'] == channel_name] if cultivation_df is not None and '创角渠道名称' in cultivation_df else None
                current_top20 = top20_df[top20_df['创角渠道名称'] == channel_name] if top20_df is not None and '创角渠道名称' in top20_df else None
                current_new_top = new_top_df[new_top_df['创角渠道名称'] == channel_name] if new_top_df is not None and '创角渠道名称' in new_top_df else None
                result['recharge_distribution'] = analyze_recharge_distribution(current_rc_dist, channel_name) if current_rc_dist is not None else None
                result['activity_analysis'] = analyze_activity_data(current_activity, channel_name) if current_activity is not None else None
                result['activity2_analysis'] = analyze_activity2_data(current_activity2, channel_name) if current_activity2 is not None else None
                result['cultivation_analysis'] = analyze_cultivation_data(current_cultivation, channel_name) if current_cultivation is not None else None
                result['top20_analysis'] = analyze_top20_data(current_top20, channel_name) if current_top20 is not None else None
                result['new_top_analysis'] = analyze_new_top_data(current_new_top, channel_name) if current_new_top is not None else None
                if '汇总' in channel_name:
                    result_key = f"channels-{version}-{channel_name}"
                else:
                    result_key = f"all-{version}-{channel_name}"
                channel_results[result_key] = result
                # 构建渠道级别的原始数据上下文
                channel_raw_data_context = {
                    'original_dataframe_info': {
                        'shape': channel_df.shape if channel_df is not None else (0, 0),
                        'columns': channel_df.columns.tolist() if channel_df is not None else [],
                        'sample_data': channel_df.head(5).to_dict('records') if channel_df is not None and not channel_df.empty else []
                    },
                    # 使用经过分析处理的数据，与第一次分析保持一致
                    'recharge_distribution_raw': result['recharge_distribution']['all_data'].to_dict('records') if result['recharge_distribution'] and 'all_data' in result['recharge_distribution'] and not result['recharge_distribution']['all_data'].empty else [],
                    'activity_raw': result['activity_analysis'].to_dict('records') if result['activity_analysis'] is not None and not result['activity_analysis'].empty else [],
                    'activity2_raw': result['activity2_analysis'].to_dict('records') if result['activity2_analysis'] is not None and not result['activity2_analysis'].empty else [],
                    'cultivation_raw': result['cultivation_analysis'].to_dict('records') if result['cultivation_analysis'] is not None and not result['cultivation_analysis'].empty else [],
                    'top20_raw': result['top20_analysis'].to_dict('records') if result['top20_analysis'] is not None and not result['top20_analysis'].empty else [],
                    'new_top_raw': result['new_top_analysis'].to_dict('records') if result['new_top_analysis'] is not None and not result['new_top_analysis'].empty else [],
                    'version_info': version,
                    'analysis_mode': 'channel_specific',
                    'channel_name': channel_name
                }

                ai_task = {
                    'channel_name': channel_name,
                    'metrics_total': result['metrics_total'],
                    'anomalies': result['anomalies'],
                    'last_7_days': result['last_7_days'],
                    'previous_7_days': result['previous_7_days'],
                    'new_user_data': None,
                    'recharge_distribution': result['recharge_distribution'],
                    'activity_analysis': result['activity_analysis'],
                    'activity2_analysis': result['activity2_analysis'],
                    'cultivation_analysis': result['cultivation_analysis'],
                    'top20_analysis': result['top20_analysis'],
                    'new_top_analysis': result['new_top_analysis'],
                    'raw_data_context': channel_raw_data_context,
                    'result_key': result_key
                }
                ai_analysis_tasks.append(ai_task)
            if ai_analysis_tasks:
                max_threads = current_config.get("MAX_THREADS", 8)
                max_workers = min(max_threads, len(ai_analysis_tasks))
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_task = {executor.submit(ai_analysis_task, task): task for task in ai_analysis_tasks}
                    for future in concurrent.futures.as_completed(future_to_task):
                        task = future_to_task[future]
                        try:
                            channel_name, analysis_text = future.result()
                            result_key = task['result_key']
                            channel_results[result_key]['analysis_text'] = analysis_text
                        except Exception as e:
                            print(f"\u274c 渠道 {task['channel_name']} 的AI分析失败: {e}")
                            traceback.print_exc()
                            result_key = task['result_key']
                            channel_results[result_key]['analysis_text'] = f"渠道 {task['channel_name']} 的AI分析失败，请查看日志了解详情。"
                analysis_results_final.update(channel_results)
        else:
            channel_data_map = separate_data_by_channel(version_df)
            if not channel_data_map:
                continue
            target_channel_orig = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "")
            channels_to_process = {}
            if target_channel_config and target_channel_config != "all":
                channel_list = [ch.strip() for ch in target_channel_orig.split(',') if ch.strip()]
                if len(channel_list) > 1:
                    for name, df_ch in channel_data_map.items():
                        for channel in channel_list:
                            if channel.lower() in name.lower():
                                channels_to_process[name] = df_ch
                else:
                    for name, df_ch in channel_data_map.items():
                        if target_channel_orig.lower() in name.lower():
                            channels_to_process[name] = df_ch
                if not channels_to_process:
                    channels_to_process = channel_data_map
            else:
                min_dau_threshold = CHANNEL_FILTER_CONFIG.get("MIN_DAU_THRESHOLD", 0)
                if min_dau_threshold > 0:
                    filtered_channels = {}
                    for name, df_ch in channel_data_map.items():
                        latest_dau = get_latest_dau_from_api(name, version)
                        if latest_dau >= min_dau_threshold:
                            filtered_channels[name] = df_ch
                        else:
                            pass
                    if filtered_channels:
                        pass
                    channels_to_process = filtered_channels
                else:
                    channels_to_process = channel_data_map
            channel_results = {}
            ai_analysis_tasks = []
            for channel_name, channel_df in channels_to_process.items():
                result = analyze_channel_data(channel_df, channel_name, version=version)
                if result is None:
                    continue
                current_rc_dist = rc_dist_df[rc_dist_df['创角渠道名称'] == channel_name] if rc_dist_df is not None and '创角渠道名称' in rc_dist_df else None
                current_activity = activity_df[activity_df['创角渠道名称'] == channel_name] if activity_df is not None and '创角渠道名称' in activity_df else None
                current_activity2 = activity2_df[activity2_df['创角渠道名称'] == channel_name] if activity2_df is not None and '创角渠道名称' in activity2_df else None
                current_cultivation = cultivation_df[cultivation_df['创角渠道名称'] == channel_name] if cultivation_df is not None and '创角渠道名称' in cultivation_df else None
                current_top20 = top20_df[top20_df['创角渠道名称'] == channel_name] if top20_df is not None and '创角渠道名称' in top20_df else None
                current_new_top = new_top_df[new_top_df['创角渠道名称'] == channel_name] if new_top_df is not None and '创角渠道名称' in new_top_df else None
                result['recharge_distribution'] = analyze_recharge_distribution(current_rc_dist, channel_name) if current_rc_dist is not None else None
                result['activity_analysis'] = analyze_activity_data(current_activity, channel_name) if current_activity is not None else None
                result['activity2_analysis'] = analyze_activity2_data(current_activity2, channel_name) if current_activity2 is not None else None
                result['cultivation_analysis'] = analyze_cultivation_data(current_cultivation, channel_name) if current_cultivation is not None else None
                result['top20_analysis'] = analyze_top20_data(current_top20, channel_name) if current_top20 is not None else None
                result['new_top_analysis'] = analyze_new_top_data(current_new_top, channel_name) if current_new_top is not None else None
                result_key = f"{version}-{channel_name}"
                channel_results[result_key] = result
                ai_task = {
                    'channel_name': channel_name,
                    'metrics_total': result['metrics_total'],
                    'anomalies': result['anomalies'],
                    'last_7_days': result['last_7_days'],
                    'previous_7_days': result['previous_7_days'],
                    'new_user_data': None,
                    'recharge_distribution': result['recharge_distribution'],
                    'activity_analysis': result['activity_analysis'],
                    'activity2_analysis': result['activity2_analysis'],
                    'cultivation_analysis': result['cultivation_analysis'],
                    'top20_analysis': result['top20_analysis'],
                    'new_top_analysis': result['new_top_analysis'],
                    'result_key': result_key
                }
                ai_analysis_tasks.append(ai_task)
            if ai_analysis_tasks:
                max_threads = current_config.get("MAX_THREADS", 8)
                max_workers = min(max_threads, len(ai_analysis_tasks))
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_task = {executor.submit(ai_analysis_task, task): task for task in ai_analysis_tasks}
                    for future in concurrent.futures.as_completed(future_to_task):
                        task = future_to_task[future]
                        try:
                            channel_name, analysis_text = future.result()
                            result_key = task['result_key']
                            channel_results[result_key]['analysis_text'] = analysis_text
                        except Exception as e:
                            print(f"\u274c 渠道 {task['channel_name']} 的AI分析失败: {e}")
                            traceback.print_exc()
                            result_key = task['result_key']
                            channel_results[result_key]['analysis_text'] = f"渠道 {task['channel_name']} 的AI分析失败，请查看日志了解详情。"
                analysis_results_final.update(channel_results)
    if existing_results:
        analysis_results_final.update(existing_results)
    valid_analysis_results = {k: v for k, v in analysis_results_final.items() if v is not None}
    if not valid_analysis_results:
        print("\n错误: 没有有效的分析结果可以导出到Excel。")
    elif excel_output_file:
        export_to_excel(valid_analysis_results, excel_output_file)
    return valid_analysis_results

def save_ai_api_content(channel_name, system_prompt, user_prompt, api_request_data, analysis_date=None):
    """
    保存提交给AI API的完整内容到aiapi文件夹
    
    Args:
        channel_name: 渠道名称
        system_prompt: 系统提示词
        user_prompt: 用户提示词
        api_request_data: API请求数据
        analysis_date: 分析日期 (可以是字符串、datetime.date对象或None)
    """
    try:
        # 创建aiapi文件夹
        aiapi_dir = "aiapi"
        if not os.path.exists(aiapi_dir):
            os.makedirs(aiapi_dir)
        
        # 处理分析日期 - 支持字符串、datetime对象或None
        if analysis_date is None:
            analysis_date_obj = datetime.now().date()
            analysis_date_str = analysis_date_obj.strftime("%Y-%m-%d")
        elif isinstance(analysis_date, str):
            # 如果是字符串，尝试解析为日期对象
            if analysis_date:
                try:
                    # 尝试不同的日期格式
                    if len(analysis_date) == 10 and '-' in analysis_date:  # YYYY-MM-DD
                        analysis_date_obj = datetime.strptime(analysis_date, "%Y-%m-%d").date()
                    elif len(analysis_date) == 8:  # YYYYMMDD
                        analysis_date_obj = datetime.strptime(analysis_date, "%Y%m%d").date()
                    else:
                        # 其他格式，使用当前日期
                        analysis_date_obj = datetime.now().date()
                    analysis_date_str = analysis_date_obj.strftime("%Y-%m-%d")
                except ValueError:
                    # 解析失败，使用当前日期
                    analysis_date_obj = datetime.now().date()
                    analysis_date_str = analysis_date_obj.strftime("%Y-%m-%d")
            else:
                # 空字符串，使用当前日期
                analysis_date_obj = datetime.now().date()
                analysis_date_str = analysis_date_obj.strftime("%Y-%m-%d")
        elif hasattr(analysis_date, 'strftime'):
            # 如果是datetime对象
            analysis_date_obj = analysis_date
            analysis_date_str = analysis_date.strftime("%Y-%m-%d")
        else:
            # 其他类型，使用当前日期
            analysis_date_obj = datetime.now().date()
            analysis_date_str = analysis_date_obj.strftime("%Y-%m-%d")
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_channel_name = re.sub(r'[<>:"/\\|?*]', '_', channel_name)  # 清理文件名中的非法字符
        filename = f"ai_api_content_{safe_channel_name}_{timestamp}.txt"
        filepath = os.path.join(aiapi_dir, filename)
        
        # 准备完整的API内容
        api_content = {
            "metadata": {
                "channel_name": channel_name,
                "analysis_date": analysis_date_str,
                "timestamp": timestamp,
                "ai_model": AI_CONFIG.get("MODEL", "unknown"),
                "api_base_url": AI_CONFIG.get("BASE_URL", "unknown")
            },
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "api_request_data": api_request_data
        }
        
        # 写入文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("AI API 完整输入内容\n")
            f.write("=" * 80 + "\n")
            f.write(f"渠道名称: {channel_name}\n")
            f.write(f"分析日期: {analysis_date_str}\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"AI模型: {AI_CONFIG.get('MODEL', 'unknown')}\n")
            f.write(f"API地址: {AI_CONFIG.get('BASE_URL', 'unknown')}\n")
            f.write("=" * 80 + "\n\n")
            
            f.write("【1. 系统提示词 (System Prompt)】\n")
            f.write("-" * 50 + "\n")
            f.write(system_prompt + "\n\n")
            
            f.write("【2. 用户提示词 (User Prompt)】\n")
            f.write("-" * 50 + "\n")
            f.write(user_prompt + "\n\n")
            
            f.write("【3. 完整API请求数据 (JSON格式)】\n")
            f.write("-" * 50 + "\n")
            f.write(json.dumps(api_request_data, ensure_ascii=False, indent=2) + "\n\n")
            
            f.write("【4. 配置信息】\n")
            f.write("-" * 50 + "\n")
            f.write(f"Temperature: {AI_CONFIG.get('TEMPERATURE', 1)}\n")
            f.write(f"Max Tokens: {AI_CONFIG.get('MAX_TOKENS', 65536)}\n")
            f.write(f"Max Threads: {AI_CONFIG.get('MAX_THREADS', 16)}\n")
            f.write("=" * 80 + "\n")
        
        print(f"✅ AI API输入内容已保存: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"❌ 保存AI API内容时出错: {e}")
        print(f"🔍 错误详情: {traceback.format_exc()}")
        return None

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='分析渠道数据并生成报告 (重构版)')
    parser.add_argument('--d_version', help='D版本CSV文件路径')
    parser.add_argument('--e_version', help='E版本CSV文件路径')
    parser.add_argument('--d_rc_dist', help='D版本充值分布CSV文件路径')
    parser.add_argument('--e_rc_dist', help='E版本充值分布CSV文件路径')
    parser.add_argument('--d_activity', help='D版本活动CSV文件路径')
    parser.add_argument('--e_activity', help='E版本活动CSV文件路径')
    parser.add_argument('--d_growth', help='D版本养成线(Growth/Cultivation)CSV文件路径')
    parser.add_argument('--e_growth', help='E版本养成线(Growth/Cultivation)CSV文件路径')
    parser.add_argument('--d_top20', help='D版本TOP20用户CSV文件路径')
    parser.add_argument('--e_top20', help='E版本TOP20用户CSV文件路径')
    parser.add_argument('--output', help='输出Excel文件路径')
    parser.add_argument('--date', help='分析日期，格式为YYYYMMDD')
    args = parser.parse_args()
    csv_files = {}
    if args.d_version: csv_files["D_VERSION"] = args.d_version
    if args.e_version: csv_files["E_VERSION"] = args.e_version
    if args.d_rc_dist: csv_files["D_RC_DIST"] = args.d_rc_dist
    if args.e_rc_dist: csv_files["E_RC_DIST"] = args.e_rc_dist
    if args.d_activity: csv_files["D_ACTIVITY"] = args.d_activity
    if args.e_activity: csv_files["E_ACTIVITY"] = args.e_activity
    if args.d_growth: csv_files["D_GROWTH"] = args.d_growth
    if args.e_growth: csv_files["E_GROWTH"] = args.e_growth
    if args.d_top20: csv_files["D_TOP20"] = args.d_top20
    if args.e_top20: csv_files["E_TOP20"] = args.e_top20
    analysis_dt = None
    if args.date:
        try:
            analysis_dt = datetime.strptime(args.date, '%Y%m%d').date()
        except ValueError:
            print(f"错误: 日期格式无效 '{args.date}'，应为 YYYYMMDD。将使用当前日期。")
    main(csv_files_abs=csv_files if csv_files else None,
         excel_filename_abs=args.output,
         analysis_date=analysis_dt)
