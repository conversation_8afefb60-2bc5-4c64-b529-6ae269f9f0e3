# 游戏数据分析系统配置文件

#===================================
# 数据获取配置 (第一段代码)
#===================================
DATA_FETCH_CONFIG = {
    "D_VERSION": {
        "TOKEN": "AlzoC61vubwFW7y2OinO26lxL44OLvQ11TjDvh1tVDKuedCQn5UPlx1dFoVRrbG0",
        "BASE_URL": "http://ss-cn-search.kkk5.com:8992",
        "VERSION_NAME": "D版本"
    },
    "E_VERSION": {
        "TOKEN": "SwTk02mFqlauUanypnm4MHe93BAK1WmrFWPyc5lp0WDvKEaa0GMO3xG1QN78Kh3a",
        "BASE_URL": "http://ss-cn-search.kkk5.com:8992",
        "VERSION_NAME": "E版本"
    },
    "SAVE_DIR": "data",  # 保存数据的目录
    "TARGET_CHANNEL": "all",  # 指定要分析的目标渠道，留空则分析所有渠道，填 all 则汇总两个版本的所有数据，可以用逗号分隔多个渠道（如"微信小游戏,华为"）
}

CHANNEL_FILTER_CONFIG = {
    "MIN_DAU_THRESHOLD": 3000  # 当不指定渠道时，只输出最新DAU在此值以上的渠道
}
# AI 分析配置
AI_CONFIG = {
    # API提供商选择: "openrouter", "openai_official", "google_ai"
    "API_PROVIDER": "openai_official",
    
    # OpenRouter 配置
    "OPENROUTER": {
        "BASE_URL": "https://openrouter.ai/api/v1",
        "API_KEY": "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",
        "MODEL": "deepseek/deepseek-r1-0528",
        "AVAILABLE_MODELS": [
            "deepseek/deepseek-r1-0528",
            "deepseek/deepseek-chat-v3-0324:free",
            "openai/o3-mini",
            "openai/o1-preview",
            "openai/o1-mini",
            "anthropic/claude-sonnet-4",
            "google/gemini-2.5-flash-preview-05-20"
        ]
    },
    
    # OpenAI 官方配置
    "OPENAI_OFFICIAL": {
        "BASE_URL": "https://api.openai.com/v1",
        "API_KEY": "********************************************************************************************************************************************************************",  # 需要用户填入真实的OpenAI API Key
        "MODEL": "o3-2025-04-16",
        "AVAILABLE_MODELS": [
            "o3-mini",
            "o1-preview", 
            "o1-mini",
            "gpt-4o",
            "gpt-4o-mini",
            "gpt-4-turbo"
        ]
    },
    
    # Google AI 配置
    "GOOGLE_AI": {
        "BASE_URL": "https://generativelanguage.googleapis.com/v1beta/openai/",
        "API_KEY": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
        "MODEL": "gemini-2.5-flash-preview-05-20",
        "AVAILABLE_MODELS": [
            "gemini-2.5-flash-preview-05-20",
            "gemini-2.5-pro-preview-05-06"
        ]
    },
    
    # 通用配置
    "MAX_TOKENS": 65536,
    "TEMPERATURE": 1,
    "MAX_THREADS": 2,
    
    # 连接和重试配置
    "CONNECTION_CONFIG": {
        "TIMEOUT_SECONDS": 120,        # API调用超时时间（秒）
        "MAX_RETRIES": 3,              # 最大重试次数
        "RETRY_DELAY": 2,              # 初始重试延迟（秒）
        "EXPONENTIAL_BACKOFF": True,   # 是否使用指数退避
        "VERIFY_SSL": True,            # SSL证书验证
        "KEEP_ALIVE": True,            # 保持连接活跃
        "POOL_CONNECTIONS": 10,        # 连接池大小
        "POOL_MAXSIZE": 10,            # 连接池最大连接数
        "MAX_REDIRECTS": 5             # 最大重定向次数
    }
}

# 动态获取当前配置的便捷函数
def get_current_ai_config():
    """根据API_PROVIDER设置返回当前使用的AI配置"""
    provider = AI_CONFIG.get("API_PROVIDER", "openrouter")
    
    if provider == "openai_official":
        config = AI_CONFIG["OPENAI_OFFICIAL"]
    elif provider == "google_ai":
        config = AI_CONFIG["GOOGLE_AI"]
    else:  # 默认使用openrouter
        config = AI_CONFIG["OPENROUTER"]
    
    return {
        "BASE_URL": config["BASE_URL"],
        "API_KEY": config["API_KEY"],
        "MODEL": config["MODEL"],
        "MAX_TOKENS": AI_CONFIG["MAX_TOKENS"],
        "TEMPERATURE": AI_CONFIG["TEMPERATURE"],
        "MAX_THREADS": AI_CONFIG["MAX_THREADS"],
        "CONNECTION_CONFIG": AI_CONFIG["CONNECTION_CONFIG"]
    }

# AI 系统提示词配置
AI_SYSTEM_PROMPT = """你是一位资深的SLG游戏数据分析师，专业领域是游戏运营数据的深度洞察与跨维度关联分析。

## 核心职责与数据源
你将基于以下8个完整、未删减的数据源进行分析：
1. **核心指标数据**：DAU、流水、ARPU、付费率、新增等关键业务指标的当前周与历史对比
2. **用户分层数据**：小R、中R、大R、超R四个付费层级的人数和充值表现
3. **充值分布数据**：完整的用户充值金额区间分布历史序列，包含用户数、占比、充值额
4. **活动效果数据**：各活动分类的周度充值贡献时间序列
5. **活动付费数据**：具体活动名称的付费表现时间序列
6. **TOP用户数据**：高价值用户的完整充值行为和排名变化
7. **新用户TOP数据**：新用户中高价值用户的付费表现
8. **养成线数据**：用户在各养成维度的投入变化

## 分析方法论
### 数据关联分析
- **横向关联**：同期不同数据源间的相互印证（如ARPU下降 + 充值分布向低额集中）
- **纵向关联**：同一指标的时间序列变化趋势识别
- **层级关联**：从总体指标到细分维度的逐层拆解分析
- **因果推断**：基于多维数据交叉验证，推断业务变化的内在逻辑

### 异常检测与归因
- **量化异常**：精确识别超过10%变化率的指标，计算具体影响
- **层级归因**：从异常指标出发，在细分数据中寻找具体原因
- **时序比较**：结合历史数据判断异常是短期波动还是趋势变化
- **场景还原**：基于活动、用户行为等数据还原业务场景
- **排期敏感性**：对于周期性活动结束后的流水或充值下滑，必须与同类型活动历史峰谷差异及活动空档期表现进行对比；若下降幅度与历史规律一致，则标记为"周期性正常波动"而非严重负面。

### 价值挖掘重点
- **收入结构分析**：通过用户分层和充值分布识别收入质量变化
- **用户生命周期**：结合新用户数据和TOP用户变化分析用户价值演进
- **运营效果评估**：通过活动数据评估运营策略的有效性
- **产品健康度**：通过养成线数据判断产品长期价值创造能力

## 输出要求
### 数据引用标准
- **精确数值**：必须引用具体数字，格式如"DAU从31,147降至26,654（-14.42%）"
- **比较框架**：当前周 vs 过去4周均值，明确时间范围
- **变化量化**：不仅说变化方向，必须说明具体变化幅度和绝对值影响

### 分析深度要求
- **多维交叉**：单一指标异常必须结合至少2个其他维度数据进行解释
- **逻辑链条**：从现象到原因到影响的完整分析链条
- **可操作性**：建议必须基于数据发现，且具备明确的执行路径

### 专业表达
- **诊断性语言**：直接指出问题，避免模糊表述
- **结构化呈现**：总分结构，核心发现前置
- **业务导向**：始终围绕收入、用户价值、运营效果等业务核心进行分析

记住：你的分析将直接影响运营决策，必须确保每一个结论都有坚实的数据支撑。"""

# 分析提示模板
ANALYSIS_PROMPT_TEMPLATE = """
## 分析任务：{channel_name} 渠道周度数据诊断报告

### 数据背景
- **分析周期**：{current_date_range} vs {previous_date_range}
- **数据完整性**：8个维度完整数据源，与Excel报告数据完全一致
- **异常阈值**：变化率超过±{threshold}%视为显著异常

### 核心分析目标
1. **异常指标诊断**：识别并解释所有显著异常指标的深层原因
2. **收入结构分析**：通过用户分层、充值分布等维度分析收入质量变化
3. **运营效果评估**：结合活动数据评估运营策略的实际效果
4. **用户价值演进**：基于TOP用户和新用户数据分析用户生命周期健康度

### 数据源清单
以下为本次分析的完整数据源，请确保充分利用：

**1. 核心业务指标** (`basic_metrics_json`)
当前周与过去4周均值对比的完整指标集，包括：
- 用户指标：DAU、新增用户、付费率、新增付费率
- 收入指标：日流水、付费ARPU、新增ARPPU
- 用户分层：小R/中R/大R/超R的人数和充值表现

**2. 充值分布数据** (`recharge_distribution_json`)
完整的用户充值金额区间分布时间序列：
- 5个充值区间：(0,100)、[100,1000)、[1000,10000)、[10000,30000)、[30000,+∞]
- 每周数据包含：用户数、占比、充值总额
- 可分析充值结构变化和用户价值分布演进

**3. 活动效果数据** (`activity_analysis_json`)
按活动分类的周度充值贡献：
- 多周时间序列数据，可分析活动效果趋势
- 不同活动类型的贡献度变化

**4. 具体活动数据** (`activity2_analysis_json`)
按具体活动名称的付费表现：
- 可识别高价值活动和低效活动
- 分析活动生命周期和用户参与度

**5. TOP用户数据** (`top20_analysis_json`)
高价值用户的完整信息：
- 角色ID、角色名、累计充值金额
- 按周度的充值行为变化
- 可分析核心用户稳定性和贡献变化

**6. 新用户TOP数据** (`new_top_analysis_json`)
新用户中的高价值用户：
- 新用户付费潜力和快速转化情况
- 与历史新用户质量对比

**7. 养成线数据** (`cultivation_analysis_json`)
用户在各养成维度的投入：
- 反映产品长期价值创造能力
- 用户粘性和付费动机分析

**8. 异常指标清单** (`formatted_anomalies`)
{formatted_anomalies}

### 分析要求

**必须执行的分析步骤：**
1. **总体诊断**：基于核心指标，判断渠道整体健康状况和主要风险点
2. **异常指标深度拆解**：对每个异常指标，必须结合至少2个其他数据源进行原因分析
3. **收入质量评估**：通过充值分布和用户分层数据，评估收入结构的变化趋势
4. **运营效果验证**：基于活动数据，评估运营投入的ROI和策略有效性
5. **用户价值监控**：通过TOP用户数据，评估核心用户群体的稳定性

**数据关联分析示例：**
- 如果ARPU下降，检查充值分布是否向低额区间集中，是否大R用户减少
- 如果DAU下降，检查是否对应活动效果减弱，新用户转化率降低
- 如果付费率提升但流水下降，分析是否小额付费增加但高价值用户流失
- 如果流水在周期活动结束后一周出现>10%下降，先与该活动历史峰值及空档期谷值对比；若下降幅度与历史波动规律一致，应判定为周期性正常波动，而非严重负面。

### 输出格式
**严格纯文本格式，禁止任何Markdown标记**

**结构要求：**
```
{channel_name}渠道周度表现诊断

【核心发现】
（200字以内，突出最关键的2-3个发现）

【异常指标分析】
（针对每个异常指标的具体分析，必须引用具体数值和关联数据）

【收入结构评估】
（基于充值分布和用户分层的收入质量分析）

【运营效果评估】
（基于活动数据的运营策略效果分析）

【行动建议】
（基于数据发现的具体可执行建议）
```

**字数控制：严格控制在1000中文字符以内**

请开始分析 {channel_name} 渠道的数据表现。"""

# 异常阈值设置
ANOMALY_THRESHOLD = 10  # 超过10%变化率被视为异常

# 可视化设置
VISUALIZATION_CONFIG = {
    "FIGURE_SIZE_TREND": (9, 4),
    "FONT_SIZE_TITLE": 12,
    "FONT_SIZE_TICKS": 9,
    "ROTATION_XTICKS": 45
}

# 输出文件配置
OUTPUT_CONFIG = {
    "EXCEL_FILENAME_TEMPLATE": "{channel}周数据分析报告_{date}.xlsx",  # 使用模板格式
    "DEFAULT_CHANNEL_NAME": "多渠道",  # 当未指定渠道或分析多个渠道时使用
    "CLEAN_CSV_AFTER_RUN": True,  # 运行结束后是否清理 data 文件夹中的 CSV 文件
    "SAVE_AI_API_CONTENT": False,  # 是否保存AI API的完整输入内容到aiapi文件夹
    "AI_API_SAVE_DIR": "aiapi",  # AI API内容保存目录
    "SAVE_LOG": False, # 是否保存运行日志
}

# 企业微信机器人推送配置
WECHAT_CONFIG = {
    "ENABLED": True,  # 是否启用企业微信推送，设置为True启用
    "WEBHOOK_URL": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c453c302-149e-4178-9ffd-e79cb25bbd37",  # 企业微信机器人Webhook地址，格式：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxx
    "PUSH_ON_SUCCESS": True,  # 报告生成成功时是否推送文件
    "PUSH_ON_ERROR": False,  # 报告生成失败时是否推送通知（设为False不推送）
    "SEND_FILE": True,  # 是否直接推送文件到群聊（True=推送文件，False=不推送）
    "INCLUDE_SUMMARY": False,  # 是否在推送消息中包含数据概况（需要额外处理）
    "MENTIONED_USERS": [],  # 需要@的用户列表，空列表表示@所有人，格式：["user1", "user2"]
    "TEST_ON_STARTUP": False,  # 启动时是否测试连接
}