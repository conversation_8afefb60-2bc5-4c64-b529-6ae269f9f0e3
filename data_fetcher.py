import requests
import json
import os
import concurrent.futures
from datetime import datetime, timedelta, date
from config import DATA_FETCH_CONFIG # Keep config import
import traceback # Keep traceback import
import pandas as pd

# --- Helper Functions ---

def get_first_day_of_week(base_date=None, weekday_start=4):
    """获取指定日期所在周的第一天，默认周四为一周的第一天"""
    if base_date is None:
        base_date = datetime.now().date()
    days_diff = (base_date.weekday() - weekday_start) % 7
    return base_date - timedelta(days=days_diff)

def update_dates_in_query(query_json, analysis_date=None):
    """更新查询中的日期字段，支持自定义分析日期，并使用周为单位获取35天数据"""
    today = datetime.now().date()
    if analysis_date:
        base_date = analysis_date
    else:
        base_date = today

    end_date = base_date - timedelta(days=1)
    start_date = end_date - timedelta(days=34)

    current_weekday = base_date.weekday() + 1

    if "eventView" in query_json and "firstDayOfWeek" in query_json["eventView"]:
        original_first_day = query_json["eventView"]["firstDayOfWeek"]
        query_json["eventView"]["firstDayOfWeek"] = current_weekday
        # if original_first_day != current_weekday: # 移除自动调整提示
        #     weekday_names = ['一','二','三','四','五','六','日']
        #     original_name = weekday_names[original_first_day-1 if original_first_day > 0 else 6]
        #     current_name = weekday_names[current_weekday-1]
        #     print(f"  ⚠️ 自动调整周起始日: 从 周{original_name} 改为 周{current_name}")

    days_range = (end_date - start_date).days + 1
    days_key = f"1-{days_range}"
    start_time_str = f"{start_date} 00:00:00"
    end_time_str = f"{end_date} 23:59:59"
    current_date_str = base_date.strftime("%Y-%m-%d")

    # print(f"  日期范围: {start_date} 到 {end_date} (共 {days_range} 天)") # 移除日期范围提示

    if "eventView" in query_json:
        query_json["eventView"]["startTime"] = start_time_str
        query_json["eventView"]["endTime"] = end_time_str
        query_json["eventView"]["recentDay"] = days_key
    else:
        # 保留关键结构警告
        print("⚠️ 警告: 查询JSON中缺少 'eventView' 键，无法更新日期。")

    update_count = update_specified_date(query_json, current_date_str)
    # print(f"  已更新 {update_count} 个 specifiedClusterDate 为 {current_date_str}") # 移除更新计数提示

    return query_json, start_date, end_date

def update_specified_date(obj, current_date_str):
    """递归更新所有specifiedClusterDate字段"""
    count = 0
    if isinstance(obj, dict):
        for key, value in obj.items():
            if key == "specifiedClusterDate":
                obj[key] = current_date_str
                count += 1
            elif isinstance(value, (dict, list)):
                count += update_specified_date(value, current_date_str)
    elif isinstance(obj, list):
        for item in obj:
            if isinstance(item, (dict, list)):
                count += update_specified_date(item, current_date_str)
    return count

def process_channel_filter(query_json, function_name="未知函数", query_file_path=""):
    """
    统一处理查询JSON中的渠道过滤逻辑
    """
    target_channel_config = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "").strip()
    target_channel_lower = target_channel_config.lower() if target_channel_config else ""

    version = "E" if "e_" in query_file_path.lower() else "D"
    channel_field_name = "cre_package_id@channel" if version == "E" else "create_package_id@channel"

    is_rc_dist = False
    if "eventView" in query_json and "events" in query_json and isinstance(query_json["events"], list):
        for event in query_json["events"]:
            if isinstance(event, dict) and "intervalType" in event and "quota" in event:
                is_rc_dist = True
                break

    if is_rc_dist:
        if target_channel_lower == "all" and "eventView" in query_json and "groupBy" in query_json["eventView"] and isinstance(query_json["eventView"]["groupBy"], list):
            query_json["eventView"]["groupBy"] = [
                g for g in query_json["eventView"]["groupBy"]
                if not (isinstance(g, dict) and
                        (g.get("columnName") == "create_package_id@channel" or
                         g.get("columnName") == "cre_package_id@channel"))
            ]
        return query_json

    if "eventView" not in query_json or "filts" not in query_json["eventView"] or not isinstance(query_json["eventView"]["filts"], list):
        # 保留结构不符的警告
        print(f"     ⚠️ 警告: [{function_name}] 查询JSON结构不符合预期 (缺少eventView.filts列表)，无法处理渠道过滤。")
        return query_json

    if target_channel_lower == "all":
        query_json["eventView"]["filts"] = [
            f for f in query_json["eventView"]["filts"]
            if not (isinstance(f, dict) and
                    (f.get("columnName") == "create_package_id@channel" or
                     f.get("columnName") == "cre_package_id@channel"))
        ]
        if "groupBy" in query_json["eventView"] and isinstance(query_json["eventView"]["groupBy"], list):
            query_json["eventView"]["groupBy"] = [
                g for g in query_json["eventView"]["groupBy"]
                if not (isinstance(g, dict) and
                        (g.get("columnName") == "create_package_id@channel" or
                         g.get("columnName") == "cre_package_id@channel"))
            ]

    elif target_channel_config:
        channel_list = [ch.strip() for ch in target_channel_config.split(',') if ch.strip()]
        comparator = "equal" # API 行为：多渠道也用 equal
        if len(channel_list) == 1:
             channel_list = [target_channel_config]

        channel_filter_found = False
        for filt in query_json["eventView"]["filts"]:
            if isinstance(filt, dict) and (filt.get("columnName") == "create_package_id@channel" or filt.get("columnName") == "cre_package_id@channel"):
                filt["ftv"] = channel_list
                filt["comparator"] = comparator
                filt["columnName"] = channel_field_name
                channel_filter_found = True
                break # Assuming only one channel filter

        if not channel_filter_found:
            new_filter = {
                "columnDesc": "创角渠道名称", "columnName": channel_field_name,
                "comparator": comparator, "filterType": "SIMPLE", "ftv": channel_list,
                "specifiedClusterDate": datetime.now().strftime("%Y-%m-%d"),
                "subTableType": "vprop_dict", "tableType": "user", "timeUnit": ""
            }
            query_json["eventView"]["filts"].append(new_filter)

    else: # TARGET_CHANNEL is empty
        query_json["eventView"]["filts"] = [
            f for f in query_json["eventView"]["filts"]
            if not (isinstance(f, dict) and
                    (f.get("columnName") == "create_package_id@channel" or
                     f.get("columnName") == "cre_package_id@channel"))
        ]

    return query_json

def load_query_from_file(file_path_relative, project_root):
    """从文件加载查询配置 (Uses absolute path)"""
    abs_path = os.path.join(project_root, file_path_relative)

    try:
        if not os.path.exists(abs_path):
            # 保留文件不存在的错误
            print(f"❌ 错误：查询文件不存在 {abs_path}")
            return None
        if not os.access(abs_path, os.R_OK):
            # 保留无权限的错误
            print(f"❌ 错误：没有查询文件读取权限 {abs_path}")
            return None

        with open(abs_path, 'r', encoding='utf-8') as f:
            query_json = json.load(f)
        return query_json

    except json.JSONDecodeError as je:
        # 保留JSON解析错误
        print(f"❌ JSON解析错误 in {abs_path}: {je}")
        return None
    except Exception as e:
        # 保留其他读取错误
        print(f"❌ 读取查询文件 {abs_path} 时出错: {str(e)}")
        return None

def load_api_average_data(channel_name, explicit_version=None):
    """加载API提供的日均数据（DAU和付费率）并按周计算均值 (Removed verbose prints)"""
    api_version = "d"
    if explicit_version:
        api_version = explicit_version.lower()[0]
    elif channel_name.startswith('E-') or "E版本" in channel_name or "E 汇总" in channel_name:
        api_version = "e"
    elif "oppo" in channel_name.lower():
        api_version = "d"

    api_file_path = f"api/{api_version}_data_average.txt"
    try:
        if os.path.exists(api_file_path):
            # print(f"加载{api_version.upper()}版本API均值数据: {api_file_path}") # 移除加载提示
            data_lines = []
            try:
                with open(api_file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip() and not line.startswith("{"):
                            data_lines.append(line.strip())
            except Exception as e:
                # 保留读取错误
                print(f"读取API均值数据文件出错: {e}")
                return None

            if not data_lines:
                # 保留空文件警告
                print(f"API均值数据文件不包含有效数据: {api_file_path}")
                return None

            try:
                columns = ["日期", "渠道", "DAU", "付费率"]
                data = []
                for line in data_lines:
                    if "|" in line:
                        parts = line.split("|")
                        if len(parts) >= 4:
                            date_str = parts[0].strip()
                            channel = parts[1].strip()
                            dau = float(parts[2].strip()) if parts[2].strip() else 0
                            payment_rate = float(parts[3].strip().rstrip('%')) / 100 if parts[3].strip() else 0
                            data.append([date_str, channel, dau, payment_rate])
                    elif "\t" in line:
                        parts = line.split("\t")
                        if len(parts) >= 4:
                            date_str = parts[0].strip()
                            channel = parts[1].strip()
                            dau = float(parts[2].strip()) if parts[2].strip() else 0
                            payment_rate = float(parts[3].strip().rstrip('%')) / 100 if parts[3].strip() else 0
                            data.append([date_str, channel, dau, payment_rate])

                df = pd.DataFrame(data, columns=columns)
                df['日期'] = pd.to_datetime(df['日期'])

                target_channel = None
                for channel in df['渠道'].unique():
                    if channel.lower() in channel_name.lower():
                        target_channel = channel
                        break

                if target_channel:
                    # print(f"为渠道 '{target_channel}' 筛选API数据") # 移除筛选提示
                    df = df[df['渠道'] == target_channel].copy()

                df = df.sort_values('日期')

                # 使用数据的最小日期作为起始日期，动态调整周期划分
                start_date = df['日期'].min()
                print(f"使用数据起始日期 {start_date.strftime('%Y-%m-%d')} 作为周期划分起点")

                # 计算每行数据距离起始日期的天数，然后整除7得到周期索引
                df['天数'] = (df['日期'] - start_date).dt.days
                df['周期'] = df['天数'] // 7

                # 计算每个周期的起始日期
                df['周起始日'] = start_date + pd.to_timedelta(df['周期'] * 7, unit='D')

                # 按自定义周期聚合
                weekly_data = df.groupby('周起始日').agg({
                    "DAU": "mean", "付费率": "mean"
                }).reset_index()

                # 重命名列以保持与原代码兼容
                weekly_data = weekly_data.rename(columns={'周起始日': '日期'})
                weekly_data = weekly_data.tail(5).reset_index(drop=True)

                result = {
                    "weekly_data": weekly_data.to_dict('records'),
                    "metrics": ["DAU", "付费率"],
                    "comparison": {}
                }

                if len(weekly_data) >= 5:
                    current_week = weekly_data.iloc[-1]
                    prev_weeks = weekly_data.iloc[-5:-1]
                    prev_dau_avg = prev_weeks['DAU'].mean()
                    prev_rate_avg = prev_weeks['付费率'].mean()

                    # 导入计算变化率的函数
                    from data_analyzer import _calculate_change_rate

                    # 使用统一的函数计算变化率
                    dau_change = _calculate_change_rate(current_week['DAU'], prev_dau_avg)
                    rate_change = _calculate_change_rate(current_week['付费率'], prev_rate_avg)

                    result["comparison"] = {
                        "DAU": {"current": current_week['DAU'], "previous": prev_dau_avg, "growth_rate": dau_change},
                        "付费率": {"current": current_week['付费率'], "previous": prev_rate_avg, "growth_rate": rate_change}
                    }
                return result

            except Exception as e:
                # 保留解析错误
                print(f"解析API均值数据时出错: {e}")
                traceback.print_exc()
                return None
        else:
            # 保留文件未找到警告
            print(f"⚠️ 警告: 未找到{api_version.upper()}版本API均值数据文件: {api_file_path}")
            return None
    except Exception as e:
        # 保留加载错误
        print(f"加载API均值数据出错: {e}")
        return None

# --- Core Fetching Logic ---

# Generic Fetch Function (Reduced Repetition)
def _fetch_data_generic(version_key, api_config_key, query_file_suffix, api_endpoint, save_file_suffix,
                        function_name, save_dir_abs, project_root, analysis_date):
    """Internal generic function to fetch data from API"""
    if not save_dir_abs or not project_root:
         print(f"❌ 错误: {function_name} 需要 save_dir_abs 和 project_root 参数")
         return None

    version_config = DATA_FETCH_CONFIG.get(api_config_key)
    if not version_config:
        print(f"❌ 错误: 配置中未找到版本 {api_config_key} (for {version_key})")
        return None

    token = version_config.get("TOKEN")
    base_url = version_config.get("BASE_URL")
    version_name = version_key.split('_')[0] # e.g., D_VERSION -> D

    if not all([token, base_url]):
        print(f"❌ 错误: 版本 {api_config_key} 的配置不完整 (TOKEN, BASE_URL)")
        return None

    query_file_relative = f"api/{version_name.lower()}{query_file_suffix}"

    query_json = load_query_from_file(query_file_relative, project_root)
    if not query_json:
        # print(f"-> 获取 {version_key} 失败：无法加载查询 {query_file_relative}") # load_query... already prints error
        return None

    query_json = process_channel_filter(query_json, function_name, query_file_relative)

    # Special handling for NEW_TOP date range
    if "NEW_TOP" in version_key:
        today = datetime.now().date() if analysis_date is None else analysis_date
        end_date = today - timedelta(days=1)
        start_date = end_date - timedelta(days=6)
        if "eventView" in query_json:
            query_json["eventView"]["startTime"] = f"{start_date} 00:00:00"
            query_json["eventView"]["endTime"] = f"{end_date} 23:59:59"
            query_json["eventView"]["recentDay"] = "1-7"
            for filt in query_json["eventView"]["filts"]:
                if isinstance(filt, dict) and filt.get("columnName") == "#vp@create_time":
                     # Corrected: Use today, not current_date_str
                    filt["specifiedClusterDate"] = today.strftime("%Y-%m-%d")
        else:
             query_json, start_date, end_date = update_dates_in_query(query_json, analysis_date)
    else:
        query_json, start_date, end_date = update_dates_in_query(query_json, analysis_date)


    # print(f"  -> 正在获取: {version_key}") # 移除获取提示

    url = f"{base_url}{api_endpoint}?token={token}"
    max_retries = 3
    timeout_seconds = 60

    for attempt in range(max_retries):
        # 保留重试提示
        print(f"     查询尝试 {attempt + 1}/{max_retries}...")
        try:
            response = requests.post(
                url,
                headers={"Content-Type": "application/json"},
                json=query_json,
                timeout=timeout_seconds
            )

            if response.status_code == 200:
                os.makedirs(save_dir_abs, exist_ok=True)
                date_range_str = f"{start_date.strftime('%Y%m%d')}-{end_date.strftime('%Y%m%d')}"
                csv_filename_abs = os.path.join(
                    save_dir_abs,
                    f"{version_key}_{date_range_str}.csv"
                )

                if not response.content:
                    # 保留空响应警告
                    print(f"     ⚠️ 警告：API响应成功但内容为空 (版本 {version_key})")
                    # Special handling for GROWTH: create empty file
                    if "GROWTH" in version_key:
                         try:
                            with open(csv_filename_abs, 'w', encoding='utf-8') as f:
                                f.write("") # 写入空内容
                            # print(f"     ✅ 创建了空文件: {os.path.basename(csv_filename_abs)}") # 移除空文件创建提示
                            return csv_filename_abs # 返回空文件路径
                         except Exception as empty_file_error:
                            print(f"     ❌ 创建空文件时出错: {empty_file_error}")
                            return None
                    else:
                        continue # Retry for other types if content is empty

                try:
                    with open(csv_filename_abs, 'wb') as f:
                        f.write(response.content)

                    if os.path.exists(csv_filename_abs):
                        # print(f"     ✅ 文件已保存: {os.path.basename(csv_filename_abs)}") # 移除保存成功提示
                        return csv_filename_abs # Success!
                    else:
                        # 保留保存后检查失败错误
                        print(f"     ❌ 错误：文件保存后检查失败 {csv_filename_abs}")
                        return None # Critical error, stop retrying

                except PermissionError:
                     # 保留权限错误
                    print(f"     ❌ 错误：没有权限写入文件 {csv_filename_abs}")
                    return None
                except Exception as save_error:
                     # 保留其他保存错误
                    print(f"     ❌ 文件保存错误 {csv_filename_abs}: {save_error}")
                    return None

            else: # Non-200 status code
                 # 保留API错误状态码和响应
                print(f"     ❌ 错误：收到状态码 {response.status_code}")
                try:
                    error_text = response.text
                    print(f"       响应内容: {error_text[:300]}{'...' if len(error_text)>300 else ''}")
                except Exception:
                    print("       无法打印错误响应文本")

        except requests.RequestException as e:
            # 保留请求错误
            print(f"     ❌ 请求错误: {e}")

    # 保留最终获取失败提示
    print(f"     ❌ 获取数据失败")
    return None


# --- Specific Fetch Functions (using the generic one) ---

def fetch_event_data(version, save_dir_abs, project_root, analysis_date):
    return _fetch_data_generic(
        version, version, "_data.txt", "/open/streaming-download/event-analyze",
        "_data", "fetch_event_data", save_dir_abs, project_root, analysis_date
    )

def fetch_average_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if version == "D_AVERAGE" else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_data_average.txt", "/open/streaming-download/event-analyze",
        "_average", "fetch_average_data", save_dir_abs, project_root, analysis_date
    )

def fetch_recharge_distribution_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_rc_dist.txt", "/open/streaming-download/distribution-analyze",
        "_rc_dist", "fetch_recharge_distribution_data", save_dir_abs, project_root, analysis_date
    )

def fetch_activity_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_activity.txt", "/open/streaming-download/event-analyze",
        "_activity", "fetch_activity_data", save_dir_abs, project_root, analysis_date
    )

def fetch_top20_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_top20.txt", "/open/streaming-download/event-analyze",
        "_top20", "fetch_top20_data", save_dir_abs, project_root, analysis_date
    )

def fetch_growth_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_growth.txt", "/open/streaming-download/event-analyze",
        "_growth", "fetch_growth_data", save_dir_abs, project_root, analysis_date
    )

def fetch_activity2_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_activity2.txt", "/open/streaming-download/event-analyze",
        "_activity2", "fetch_activity2_data", save_dir_abs, project_root, analysis_date
    )

def fetch_new_top_data(version, save_dir_abs, project_root, analysis_date):
    base_version = "D_VERSION" if "D" in version else "E_VERSION"
    return _fetch_data_generic(
        version, base_version, "_new_top.txt", "/open/streaming-download/event-analyze",
        "_new_top", "fetch_new_top_data", save_dir_abs, project_root, analysis_date
    )

# --- Task Runner ---

def fetch_data_task(task_info):
    """用于线程池的数据获取任务函数"""
    task_id = task_info['id']
    task_name = task_info['name']
    version = task_info['version']
    fetch_func = task_info['func']
    save_dir_abs = task_info['save_dir_abs']
    project_root = task_info['project_root']
    analysis_date = task_info['analysis_date']
    total_steps = task_info['total_steps']

    # 保留任务开始提示
    print(f"\n[{task_id}/{total_steps}] 获取 {task_name}:")
    result = fetch_func(
        version,
        save_dir_abs,
        project_root,
        analysis_date
    )

    return version, result

def fetch_all_data(save_dir_abs, project_root, analysis_date=None):
    """使用多线程并行获取所有版本的数据"""
    # 保留总体开始提示
    print("\n正在获取所有版本的数据...")

    os.makedirs(save_dir_abs, exist_ok=True)

    total_steps = 16
    tasks = [
        {'id': 1, 'name': 'D 版本数据', 'version': 'D_VERSION', 'func': fetch_event_data},
        {'id': 2, 'name': 'E 版本数据', 'version': 'E_VERSION', 'func': fetch_event_data},
        {'id': 3, 'name': 'D 版本平均数据', 'version': 'D_AVERAGE', 'func': fetch_average_data},
        {'id': 4, 'name': 'E 版本平均数据', 'version': 'E_AVERAGE', 'func': fetch_average_data},
        {'id': 5, 'name': 'D 版本充值金额分布数据', 'version': 'D_RC_DIST', 'func': fetch_recharge_distribution_data},
        {'id': 6, 'name': 'E 版本充值金额分布数据', 'version': 'E_RC_DIST', 'func': fetch_recharge_distribution_data},
        {'id': 7, 'name': 'D 版本活动数据', 'version': 'D_ACTIVITY', 'func': fetch_activity_data},
        {'id': 8, 'name': 'E 版本活动数据', 'version': 'E_ACTIVITY', 'func': fetch_activity_data},
        {'id': 9, 'name': 'D 版本活动2数据', 'version': 'D_ACTIVITY2', 'func': fetch_activity2_data},
        {'id': 10, 'name': 'E 版本活动2数据', 'version': 'E_ACTIVITY2', 'func': fetch_activity2_data},
        {'id': 11, 'name': 'D 版本增长数据', 'version': 'D_GROWTH', 'func': fetch_growth_data},
        {'id': 12, 'name': 'E 版本增长数据', 'version': 'E_GROWTH', 'func': fetch_growth_data},
        {'id': 13, 'name': 'D 版本 TOP20 数据', 'version': 'D_TOP20', 'func': fetch_top20_data},
        {'id': 14, 'name': 'E 版本 TOP20 数据', 'version': 'E_TOP20', 'func': fetch_top20_data},
        {'id': 15, 'name': 'D 版本 新注册用户TOP 数据', 'version': 'D_NEW_TOP', 'func': fetch_new_top_data},
        {'id': 16, 'name': 'E 版本 新注册用户TOP 数据', 'version': 'E_NEW_TOP', 'func': fetch_new_top_data},
    ]

    for task in tasks:
        task['save_dir_abs'] = save_dir_abs
        task['project_root'] = project_root
        task['analysis_date'] = analysis_date
        task['total_steps'] = total_steps

    csv_files_abs = {}
    max_workers = min(8, len(tasks)) # Limit workers
    # 保留线程数提示
    print(f"使用 {max_workers} 个线程并行获取数据...")

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {executor.submit(fetch_data_task, task): task for task in tasks}

        for future in concurrent.futures.as_completed(future_to_task):
            task = future_to_task[future]
            try:
                version, result = future.result()
                csv_files_abs[version] = result
                if result:
                    # 保留任务完成提示
                    print(f"✅ 任务 [{task['id']}/{total_steps}] {task['name']} 完成")
                else:
                    # 保留任务失败提示
                    print(f"❌ 任务 [{task['id']}/{total_steps}] {task['name']} 失败")
            except Exception as e:
                 # 保留任务异常提示和traceback
                print(f"❌ 任务 [{task['id']}/{total_steps}] {task['name']} 发生异常: {e}")
                traceback.print_exc()
                csv_files_abs[task['version']] = None

    success_count = sum(1 for file in csv_files_abs.values() if file)
    # 保留最终总结
    print(f"\n数据获取完成，成功: {success_count}/{total_steps}")

    return csv_files_abs