@echo off
REM Batch script to run the Game Data Analysis Python script

REM Get the directory where this batch script is located
SET SCRIPT_DIR=%~dp0

REM Change the current directory to the script's directory
REM The /d switch ensures drive change works if necessary
cd /d "%SCRIPT_DIR%"

echo ========================================
echo Starting Game Data Analysis (main.py)...
echo Working Directory: %CD%
echo ========================================
echo.

REM Run the main Python script
REM Assumes 'python' is in your system PATH.
REM If you use 'py', change 'python' to 'py'.
python main.py

echo.
echo ========================================
echo Python script finished.
echo ========================================
echo.
pause