import os
import time
import json
from datetime import datetime, date
import pandas as pd
# Assuming data_fetcher and data_analyzer are in the same directory or accessible via PYTHONPATH
from data_fetcher import fetch_all_data
from data_analyzer import main as analyze_data
from config import DATA_FETCH_CONFIG, OUTPUT_CONFIG, WECHAT_CONFIG
from wechat_notifier import create_notifier_from_config

# --- Define Project Root ---
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# --- Define Absolute Paths based on Project Root ---
SAVE_DIR_ABS = os.path.join(PROJECT_ROOT, DATA_FETCH_CONFIG["SAVE_DIR"])
LOG_DIR_ABS = os.path.join(PROJECT_ROOT, "logs")

# --- 获取分析日期 ---
analysis_date_str = DATA_FETCH_CONFIG.get("ANALYSIS_DATE", "")
if analysis_date_str:
    try:
        analysis_date = datetime.strptime(analysis_date_str, "%Y-%m-%d").date()
    except ValueError:
        # 如果日期格式不正确，回退到当天 - 保留此警告
        print(f"警告: 指定的分析日期 '{analysis_date_str}' 格式不正确，使用当天日期")
        analysis_date = datetime.now().date()
else:
    # 默认使用当天
    analysis_date = datetime.now().date()

# --- 根据目标渠道和分析日期构建Excel文件名 ---z3.--
excel_filename_template = OUTPUT_CONFIG.get("EXCEL_FILENAME_TEMPLATE", "{channel}周数据分析报告_{date}.xlsx")
default_channel_name = OUTPUT_CONFIG.get("DEFAULT_CHANNEL_NAME", "多渠道")
target_channel = DATA_FETCH_CONFIG.get("TARGET_CHANNEL", "")
channel_name = target_channel if target_channel else default_channel_name
today_str = analysis_date.strftime("%Y%m%d")
dated_excel_filename = excel_filename_template.format(channel=channel_name, date=today_str)
EXCEL_FILENAME_ABS = os.path.join(PROJECT_ROOT, dated_excel_filename)

# --- Helper to get absolute path for config files ---
def get_config_path(relative_path):
    return os.path.join(PROJECT_ROOT, relative_path)

def ensure_query_files_exist():
    """
    确保查询配置文件存在，如果不存在则创建示例文件
    """
    query_files_relative = [
        "api/d_data.txt",
        "api/e_data.txt",
    ]

    for relative_path in query_files_relative:
        absolute_path = get_config_path(relative_path)
        if not os.path.exists(absolute_path):
            # 保留文件不存在的警告
            print(f"警告: 找不到 {absolute_path}，请确保查询配置文件存在")
            example_query = {
                 "eventView": {"startTime": "", "endTime": "", "recentDay": ""}, "filters": []
            }
            try:
                os.makedirs(os.path.dirname(absolute_path), exist_ok=True)
                with open(absolute_path, 'w', encoding='utf-8') as f:
                    json.dump(example_query, f, ensure_ascii=False, indent=2)
                # 移除创建成功提示
                # print(f"已创建示例查询文件: {absolute_path}")
            except Exception as e:
                # 保留创建失败的错误
                print(f"错误: 创建示例文件 {absolute_path} 失败: {e}")

def create_run_log(csv_files, report_file_abs):
    """
    创建运行日志
    """
    if not os.path.exists(LOG_DIR_ABS):
        os.makedirs(LOG_DIR_ABS)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOG_DIR_ABS, f"run_log_{timestamp}.txt")

    with open(log_file, 'w', encoding='utf-8') as f:
        f.write("游戏数据分析系统 - 运行日志\n")
        f.write("=" * 50 + "\n")
        f.write(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析日期: {analysis_date.strftime('%Y-%m-%d')}\n")
        f.write(f"目标渠道: {target_channel if target_channel else '全部渠道'}\n\n")
        f.write("获取的数据文件:\n")
        for key, file_path_abs in csv_files.items():
            status = "成功" if file_path_abs and os.path.exists(file_path_abs) else "失败"
            f.write(f"- {key}: {status} {file_path_abs if file_path_abs else ''}\n")
        f.write("\n生成的报告文件:\n")
        f.write(f"- {report_file_abs}\n")

    # 移除日志保存成功提示，将在最后统一显示
    # print(f"运行日志已保存: {log_file}")
    return log_file

def clean_csv_files(directory):
    """
    清理指定目录中的所有CSV文件
    """
    if not os.path.exists(directory):
        # 保留目录不存在的警告
        print(f"警告: 目录 {directory} 不存在，无法清理CSV文件")
        return

    try:
        count = 0
        for filename in os.listdir(directory):
            if filename.lower().endswith('.csv'):
                file_path = os.path.join(directory, filename)
                try:
                    os.remove(file_path)
                    count += 1
                except Exception as e:
                    # 保留删除失败的警告
                    print(f"警告: 无法删除文件 {file_path}: {e}")

        # 移除清理成功提示
        # if count > 0:
        #     print(f"清理了 {count} 个CSV文件")
    except Exception as e:
        # 保留清理过程中的错误
        print(f"清理CSV文件时出错: {e}")

def main():
    """
    主函数，执行完整的数据获取和分析流程
    """
    print("=" * 60)
    print("游戏数据分析系统 - 开始运行")
    print("=" * 60)

    # 显示关键配置信息
    print(f"目标渠道: {target_channel if target_channel else '所有渠道'}")
    print(f"分析日期: {analysis_date.strftime('%Y-%m-%d')}")
    print("📝 注意: AI API的完整输入内容将自动保存到 aiapi/ 文件夹中")
    
    # 初始化企业微信通知器
    wechat_notifier = None
    if WECHAT_CONFIG.get("ENABLED", False):
        wechat_notifier = create_notifier_from_config(WECHAT_CONFIG)
        if wechat_notifier:
            print(f"✅ 企业微信推送已启用")
            # 如果配置了启动测试，进行连接测试
            if WECHAT_CONFIG.get("TEST_ON_STARTUP", False):
                test_result = wechat_notifier.test_connection()
                if test_result.get("success"):
                    print("✅ 企业微信连接测试成功")
                else:
                    print(f"❌ 企业微信连接测试失败: {test_result.get('error')}")
        else:
            print("❌ 企业微信配置无效，推送功能已禁用")
    else:
        print("ℹ️  企业微信推送未启用")
    
    print("-" * 60)

    # 第一步: 确保查询配置文件存在 (如果需要此检查)
    # ensure_query_files_exist() # 可以根据需要启用

    is_all_channels_mode = (target_channel.lower() == "all")

    # 第二步: 获取数据
    print("\n[步骤 1/4] 开始获取游戏数据...")
    start_time = time.time()
    csv_files_abs = fetch_all_data(SAVE_DIR_ABS, PROJECT_ROOT, analysis_date)
    fetch_time = time.time() - start_time
    print(f"数据获取完成，耗时: {fetch_time:.2f} 秒")

    successful_files = {k: v for k, v in csv_files_abs.items() if v and os.path.exists(v)}
    if not successful_files:
        # 保留关键错误
        print("错误: 所有数据获取都失败，无法继续分析")
        return

    # 第三步: 分析数据
    print("\n[步骤 2/4] 开始分析数据...")
    start_time = time.time()
    analysis_results = None # 初始化

    if is_all_channels_mode:
        # 第一次分析 (汇总) - 不写入文件
        analysis_results_agg = analyze_data(successful_files, None, analysis_date)

        # 保存原始 TARGET_CHANNEL
        original_target_channel = DATA_FETCH_CONFIG["TARGET_CHANNEL"]
        DATA_FETCH_CONFIG["TARGET_CHANNEL"] = "" # 临时清空以获取所有渠道

        # 重新获取所有渠道数据
        csv_files_channels = fetch_all_data(SAVE_DIR_ABS, PROJECT_ROOT, analysis_date)

        # 提取并重命名汇总页签
        filtered_results = {}
        for key in analysis_results_agg.keys():
            if key == 'D 汇总':
                filtered_results['D版汇总'] = analysis_results_agg[key]
            elif key == 'E 汇总':
                filtered_results['E版汇总'] = analysis_results_agg[key]

        if not filtered_results:
             # 保留汇总页签未找到的警告
            print("\n警告: 未找到 'D 汇总' 或 'E 汇总' 页签，使用原始的汇总页签")
            filtered_results = analysis_results_agg
        analysis_results_agg = filtered_results # 使用过滤/重命名后的结果

        # 第二次分析 (所有渠道)，合并第一次的汇总结果，写入最终文件
        analysis_results = analyze_data(csv_files_channels, EXCEL_FILENAME_ABS, analysis_date, existing_results=analysis_results_agg)

        # 恢复原始 TARGET_CHANNEL
        DATA_FETCH_CONFIG["TARGET_CHANNEL"] = original_target_channel

    else:
        # 非 all 模式，正常分析并写入文件
        analysis_results = analyze_data(successful_files, EXCEL_FILENAME_ABS, analysis_date)

        # 确保在分析完成后才进行后续操作
        if analysis_results is None:
            print("错误: 数据分析失败，无法继续。")
            return

    analysis_time = time.time() - start_time
    print(f"数据分析完成，耗时: {analysis_time:.2f} 秒")

    # 第四步: 创建运行日志
    if OUTPUT_CONFIG.get("SAVE_LOG", True): # 根据配置决定是否创建运行日志
        print("\n[步骤 3/4] 创建运行日志...")
        log_file = create_run_log(csv_files_abs, EXCEL_FILENAME_ABS)
    else:
        print("\n[步骤 3/4] 跳过运行日志创建 (根据配置)")
        log_file = None # 日志文件未创建

    # 第五步: 清理 (如果配置了)
    cleanup_step_num = 4
    if OUTPUT_CONFIG.get("CLEAN_CSV_AFTER_RUN", False) and analysis_results is not None:
        print(f"\n[步骤 {cleanup_step_num}/4] 清理数据文件...")
        clean_csv_files(SAVE_DIR_ABS)
    else:
        # 如果不清理或分析失败，总步骤数减少
        if analysis_results is None:
            print("警告: 由于数据分析失败，跳过清理数据文件步骤")
        pass # No print needed here, step numbering adjusts implicitly in summary


    # 删除临时文件 (default.xlsx)
    if is_all_channels_mode:
        default_excel_filename = os.path.join(PROJECT_ROOT, "渠道周数据分析报告_default.xlsx")
        if os.path.exists(default_excel_filename):
            try:
                os.remove(default_excel_filename)
                # print(f"已删除临时文件: {default_excel_filename}")  # 可以取消注释以显示删除信息
            except Exception as e:
                print(f"警告: 无法删除临时文件 {default_excel_filename}: {e}")

    # --- 企业微信文件推送 ---
    if wechat_notifier and analysis_results is not None:
        if WECHAT_CONFIG.get("PUSH_ON_SUCCESS", True):
            send_file = WECHAT_CONFIG.get("SEND_FILE", True)
            if send_file:
                print(f"\n[步骤 4/4] 推送文件到企业微信...")
                
                # 仅推送文件，不发送任何通知消息
                push_result = wechat_notifier.send_file_message(EXCEL_FILENAME_ABS)
                
                if push_result.get("success"):
                    print(f"✅ 文件推送成功")
                else:
                    print(f"❌ 文件推送失败: {push_result.get('error')}")
            # 如果不推送文件，则不进行任何操作
    # 失败时不进行任何推送（根据配置PUSH_ON_ERROR=False）

    # --- 最终总结 ---
    print("\n" + "=" * 60)
    print("游戏数据分析系统 - 运行完成")
    print("=" * 60)
    print(f"总耗时: {fetch_time + analysis_time:.2f} 秒")
    print(f"报告文件: {EXCEL_FILENAME_ABS}")
    print(f"运行日志: {log_file}")
    print("=" * 60)


if __name__ == "__main__":
    main()
# --- END OF FILE main.py ---