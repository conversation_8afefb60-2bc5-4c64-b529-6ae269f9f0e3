# 渠道数据监控系统架构设计文档

## 项目概述
本系统为SLG游戏提供全渠道数据分析能力，通过自动化数据采集、AI智能分析和可视化报告生成，帮助运营团队快速掌握业务健康度。系统每日处理超过50万行数据，生成包含8个维度的深度分析报告。

## 系统架构
```mermaid
graph TD
    A[外部API] -->|数据拉取| B(data_fetcher.py)
    B --> C[原始CSV数据]
    C --> D(data_analyzer.py)
    D -->|AI分析| E[DeepSeek R1]
    E --> F[Excel分析报告]
    G[config.py] -->|配置| B
    G -->|提示词| D
    H[main.py] -->|任务调度| B
    H -->|流程控制| D
```

### 核心模块
1. **数据采集层**
   - 多线程API请求（最大并发16线程）
   - 自动重试机制（3次重试+60秒超时）
   - 渠道过滤与日期范围计算

2. **分析引擎层**
   - 数据预处理（缺失值填充/百分比转换）
   - 异常检测（>10%变化率标记）
   - 多维分析（渠道/用户分层/时间趋势）

3. **AI报告层**
   - DeepSeek R1模型集成
   - 定制化提示词工程
   - 千字内精准诊断报告生成

4. **输出层**
   - Excel可视化（动态图表嵌入）
   - 运行日志审计
   - 自动清理机制

## 配置体系
### config.py 关键配置项
```python
DATA_FETCH_CONFIG = {
    "D_VERSION": {
        "TOKEN": "AlzoC61vubwFW7y2OinO26lxL44OLvQ11TjDvh1tVDK...",
        "BASE_URL": "http://ss-cn-search.kkk5.com:8992"
    },
    "TARGET_CHANNEL": "all"  # 全渠道分析模式
}

AI_CONFIG = {
    "MODEL": "deepseek/deepseek-r1-0528",
    "MAX_TOKENS": 65536,
    "TEMPERATURE": 1.0
}
```

## 数据流水线
```mermaid
sequenceDiagram
    participant Scheduler
    participant Fetcher
    participant Analyzer
    participant AI_Engine
    
    Scheduler->>Fetcher: 启动数据采集
    Fetcher->>API: 并发请求(8种数据类型)
    API-->>Fetcher: CSV响应
    Fetcher-->>Scheduler: 文件存储路径
    Scheduler->>Analyzer: 执行分析
    Analyzer->>AI_Engine: 发送分析请求
    AI_Engine-->>Analyzer: 返回诊断报告
    Analyzer-->>Scheduler: 生成Excel+图表
    Scheduler->>Scheduler: 清理临时文件
```

## 性能指标
| 模块 | 处理能力 | 耗时 |
|------|---------|------|
| 数据采集 | 50万行/日 | 120秒 |
| AI分析 | 8维度/渠道 | 45秒 |
| 报告生成 | 20页Excel | 30秒 |

## 改进路线图
1. **V2.5 (Q3 2025)**
   - 增加数据校验机制
   - 实现PDF报告导出
   - 添加邮件自动推送

2. **V3.0 (Q4 2025)**
   - 引入实时监控看板
   - 集成预警系统
   - 支持自定义分析模板

## 部署架构
```
├── /deploy
│   ├── docker-compose.yml  # 容器化部署
│   ├── requirements.txt    # 依赖清单
│   └── entrypoint.sh       # 启动脚本
├── /src
│   ├── main.py             # 主入口
│   ├── data_fetcher.py     # 数据采集
│   ├── data_analyzer.py    # 分析引擎
│   └── config.py           # 配置中心
└── README.md               # 部署文档
```

> 文档生成时间: 2025-05-29 11:15