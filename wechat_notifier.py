import os
import requests
import json
from datetime import datetime
from typing import Optional, Dict, Any
import hashlib
import mimetypes

class WeChatNotifier:
    """企业微信机器人通知类"""
    
    def __init__(self, webhook_url: str):
        """
        初始化企业微信机器人通知器
        
        Args:
            webhook_url: 企业微信机器人的Webhook URL
        """
        self.webhook_url = webhook_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WechatBot/1.0',
            'Content-Type': 'application/json'
        })
    
    def validate_webhook_url(self) -> bool:
        """
        验证Webhook URL是否有效
        
        Returns:
            bool: URL是否有效
        """
        if not self.webhook_url:
            return False
            
        # 企业微信机器人URL格式验证
        if "qyapi.weixin.qq.com" not in self.webhook_url or "webhook" not in self.webhook_url:
            return False
            
        return True
    
    def send_text_message(self, content: str, mentioned_users: Optional[list] = None) -> Dict[str, Any]:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_users: @用户列表，None表示@所有人
            
        Returns:
            Dict: 发送结果
        """
        if not self.validate_webhook_url():
            return {"success": False, "error": "无效的Webhook URL"}
        
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 添加@用户功能
        if mentioned_users is not None:
            data["text"]["mentioned_list"] = mentioned_users
        else:
            data["text"]["mentioned_list"] = ["@all"]
        
        try:
            response = self.session.post(self.webhook_url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                return {"success": True, "message": "文本消息发送成功"}
            else:
                return {"success": False, "error": f"企业微信返回错误: {result.get('errmsg', '未知错误')}"}
                
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"发送失败: {str(e)}"}
    
    def send_markdown_message(self, content: str) -> Dict[str, Any]:
        """
        发送Markdown格式消息
        
        Args:
            content: Markdown格式的消息内容
            
        Returns:
            Dict: 发送结果
        """
        if not self.validate_webhook_url():
            return {"success": False, "error": "无效的Webhook URL"}
        
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        try:
            response = self.session.post(self.webhook_url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                return {"success": True, "message": "Markdown消息发送成功"}
            else:
                return {"success": False, "error": f"企业微信返回错误: {result.get('errmsg', '未知错误')}"}
                
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"发送失败: {str(e)}"}
    
    def upload_media(self, file_path: str) -> Dict[str, Any]:
        """
        上传媒体文件到企业微信
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 上传结果，包含media_id
        """
        if not os.path.exists(file_path):
            return {"success": False, "error": f"文件不存在: {file_path}"}
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        if file_size > 20 * 1024 * 1024:  # 20MB限制
            return {"success": False, "error": "文件大小超过20MB限制"}
        
        # 从webhook URL构建上传URL
        key = self._extract_key_from_webhook()
        if not key:
            return {"success": False, "error": "无法从webhook URL提取key参数"}
            
        upload_url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key={key}&type=file"
        
        try:
            with open(file_path, 'rb') as f:
                filename = os.path.basename(file_path)
                
                # 构建multipart/form-data
                files = {
                    'media': (filename, f, 'application/octet-stream')
                }
                
                # 移除Content-Type头，让requests自动设置multipart边界
                headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}
                
                response = requests.post(upload_url, files=files, headers=headers, timeout=60)
                response.raise_for_status()
                
                result = response.json()
                if result.get("errcode") == 0:
                    return {
                        "success": True, 
                        "media_id": result.get("media_id"),
                        "message": "文件上传成功"
                    }
                else:
                    return {"success": False, "error": f"上传失败: {result.get('errmsg', '未知错误')}"}
                    
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"上传失败: {str(e)}"}
    
    def send_file_message(self, file_path: str, description: str = "") -> Dict[str, Any]:
        """
        发送文件消息（仅推送文件，不发送任何描述信息）
        
        Args:
            file_path: 文件路径
            description: 文件描述信息（忽略，保持接口兼容性）
            
        Returns:
            Dict: 发送结果
        """
        if not self.validate_webhook_url():
            return {"success": False, "error": "无效的Webhook URL"}
        
        if not os.path.exists(file_path):
            return {"success": False, "error": f"文件不存在: {file_path}"}
        
        # 直接上传文件获取media_id，不发送任何描述信息
        upload_result = self.upload_media(file_path)
        if not upload_result.get("success"):
            return upload_result
        
        media_id = upload_result.get("media_id")
        if not media_id:
            return {"success": False, "error": "未获取到有效的media_id"}
        
        # 发送文件消息
        data = {
            "msgtype": "file",
            "file": {
                "media_id": media_id
            }
        }
        
        try:
            response = self.session.post(self.webhook_url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("errcode") == 0:
                return {"success": True, "message": f"文件 {os.path.basename(file_path)} 发送成功"}
            else:
                return {"success": False, "error": f"文件发送失败: {result.get('errmsg', '未知错误')}"}
                
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"网络请求失败: {str(e)}"}
        except Exception as e:
            return {"success": False, "error": f"文件发送失败: {str(e)}"}
    
    def send_report_notification(self, report_path: str, channel_name: str, 
                               analysis_date: str, summary_stats: Optional[Dict] = None, 
                               send_file: bool = True) -> Dict[str, Any]:
        """
        发送数据报告（仅推送文件，不发送任何通知消息）
        
        Args:
            report_path: 报告文件路径
            channel_name: 渠道名称（忽略，保持接口兼容性）
            analysis_date: 分析日期（忽略，保持接口兼容性）
            summary_stats: 汇总统计信息（忽略，保持接口兼容性）
            send_file: 是否直接推送文件，True=推送文件，False=不推送任何内容
            
        Returns:
            Dict: 发送结果
        """
        if not os.path.exists(report_path):
            return {"success": False, "error": f"报告文件不存在: {report_path}"}
        
        if send_file:
            # 仅推送文件，不发送任何通知消息
            return self.send_file_message(report_path)
        else:
            # 不推送任何内容
            return {"success": True, "message": "配置为不推送文件，跳过推送"}
    
    def _extract_key_from_webhook(self) -> str:
        """
        从webhook URL中提取key参数
        
        Returns:
            str: 提取的key
        """
        try:
            if "key=" in self.webhook_url:
                return self.webhook_url.split("key=")[-1]
        except:
            pass
        return ""
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接是否正常
        
        Returns:
            Dict: 测试结果
        """
        if not self.validate_webhook_url():
            return {"success": False, "error": "无效的Webhook URL"}
        
        test_message = f"🤖 企业微信机器人连接测试\n⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return self.send_text_message(test_message)


def create_notifier_from_config(config: Dict[str, Any]) -> Optional[WeChatNotifier]:
    """
    从配置创建通知器实例
    
    Args:
        config: 配置字典
        
    Returns:
        WeChatNotifier: 通知器实例，如果配置无效则返回None
    """
    webhook_url = config.get("WEBHOOK_URL", "")
    
    if not webhook_url:
        return None
    
    return WeChatNotifier(webhook_url) 