{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-20 23:59:59", "filts": [{"columnDesc": "内部用户", "columnName": "is_tester", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2024-11-15", "tableType": "user", "timeUnit": ""}, {"columnDesc": "活动名称", "columnName": "activities_name", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["飞龙狂欢", "龙币", "觉醒豪礼", "每日必买", "礼包", "限时礼包", "月卡", "总动员", "每日特惠", "终生一次", "赛季战令", "周卡", "VIP礼包", "丛林寻宝", "首充礼包"], "specifiedClusterDate": "2025-04-15", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-04-15", "tableType": "event", "timeUnit": ""}], "firstDayOfWeek": 5, "groupBy": [{"columnDesc": "活动名称", "columnName": "activities_name", "propertyRange": "", "tableType": "event"}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-17 00:00:00", "timeParticleSize": "week"}, "events": [{"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "充值日志.价格总和", "eventUuid": "WPkJDzx-", "filts": [], "quota": "money", "relation": "and", "type": "normal"}], "projectId": 2}