{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-20 23:59:59", "filts": [{"columnDesc": "是否内部用户", "columnName": "is_tester", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-03-26", "tableType": "user", "timeUnit": ""}], "groupBy": [{"columnDesc": "创角渠道名称", "columnName": "cre_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-17 00:00:00", "timeParticleSize": "day"}, "events": [{"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_login", "eventNameDisplay": "DAU", "eventUuid": "VJjZZEMo2DkJfNUn", "filts": [], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"customEvent": "role_pay.TRIG_USER_NUM/role_login.TRIG_USER_NUM", "customFilters": [{"filts": [{"filterType": "COMPOUND", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notequal", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "relation": "and"}], "index": 0, "relation": "and"}, {"filts": [], "index": 1, "relation": "and"}], "eventName": "付费率", "eventNameDisplay": "", "eventUuid": "_j_HYHrbv6XMgC3s", "filts": [], "format": "percent", "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}, {"index": 1, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "quotaTimeRanges": [], "relation": "and", "type": "customized"}], "projectId": 24}