{"eventView": {"endTime": "2025-04-17 23:59:59", "firstDayOfWeek": 5, "groupBy": [{"columnDesc": "创角渠道名称", "columnName": "cre_package_id@channel", "propertyRange": "", "specifiedClusterDate": "2025-03-26", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "startTime": "2025-03-14 00:00:00", "taIdMeasureVo": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}, "timeParticleSize": "week"}, "events": [{"analysis": "SUM", "analysisDesc": "总和", "eventName": "role_pay", "eventNameDisplay": "", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-04-17", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-04-17", "tableType": "event", "timeUnit": ""}, {"columnDesc": "30日内部用户（24年至今）", "columnName": "tag_20241017_1", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "intervalType": "user_defined", "quota": "price", "quotaIntervalArr": [100, 1000, 10000, 30000], "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisDesc": "总和", "eventName": "role_pay", "eventNameDisplay": "", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-04-17", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-04-17", "tableType": "event", "timeUnit": ""}, {"columnDesc": "30日内部用户（24年至今）", "columnName": "tag_20241017_1", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "intervalType": "def", "quota": "price", "relation": "and", "type": "normal"}], "projectId": 24, "interval": "[',100','100,1000','1000,10000','10000,30000','30000,']"}