{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-20 23:59:59", "filts": [{"columnDesc": "内部用户（21年至今）", "columnName": "tag_20241028_1", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-04-18", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "养成线", "columnName": "#vp@cultivate_id@cultivate_name", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["综合", "外观", "巨兽", "飞龙", "龙币", "机甲", "城建", "凤凰", "超导", "超导晶", "装备", "士兵", "宝石", "龙冠"], "specifiedClusterDate": "2025-04-15", "subTableType": "vprop_dict_v", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-04-15", "tableType": "event", "timeUnit": ""}], "firstDayOfWeek": 5, "groupBy": [{"columnDesc": "养成线", "columnName": "#vp@cultivate_id@cultivate_name", "propertyRange": "", "subTableType": "vprop_dict_v", "tableType": "event"}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-17 00:00:00", "timeParticleSize": "week"}, "events": [{"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "充值日志.价格总和", "eventUuid": "WPkJDzx-", "filts": [], "quota": "money", "relation": "and", "type": "normal"}], "projectId": 2}