{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-17 23:59:59", "filts": [{"columnDesc": "内部用户（21年至今）", "columnName": "tag_20241028_1", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-03-20", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["华为", "安卓", "苹果", "OPPO", "VIVO", "小米", "微信小游戏"], "specifiedClusterDate": "2025-03-25", "subTableType": "vprop_dict", "tableType": "user", "timeUnit": ""}], "firstDayOfWeek": 4, "groupBy": [{"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-14 00:00:00", "timeParticleSize": "day"}, "events": [{"analysisParams": "", "eventName": "LoginRole", "eventNameDisplay": "DAU", "eventUuid": "rO8ECy9lYUpKxYck", "filts": [], "format": "integer", "metricName": "dau", "quota": "", "relation": "and", "type": "normal"}, {"analysisParams": "", "eventName": "Charge,LoginRole", "eventNameDisplay": "付费率", "eventUuid": "km_e3GQMwUd7Pdcl", "filts": [], "format": "percent", "metricName": "dau_payment_rate", "quota": "", "relation": "and", "type": "normal"}], "projectId": 2}