{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-23 23:59:59", "filts": [{"columnDesc": "内部用户（21年至今）", "columnName": "tag_20241028_1", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-03-20", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["华为", "安卓", "苹果", "OPPO", "VIVO", "小米", "微信小游戏"], "specifiedClusterDate": "2025-03-25", "subTableType": "vprop_dict", "tableType": "user", "timeUnit": ""}], "firstDayOfWeek": 1, "groupBy": [{"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-20 00:00:00", "timeParticleSize": "week"}, "events": [{"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "CreateRole", "eventNameDisplay": "新增", "eventUuid": "fIUimx_3tz-t_m8c", "filts": [], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "流水", "eventUuid": "r_x3y-NtBUlfC1Hb", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}], "quota": "money", "relation": "and", "type": "normal"}, {"analysis": "PER_CAPITA_NUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "付费 ARPU", "eventUuid": "xR0hzNAIUDZ-Iryh", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}], "quota": "money", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysisParams": "", "eventName": "Charge,CreateRole", "eventNameDisplay": "新增付费率", "eventUuid": "OaV83Jj1v4RO2aJ6", "filts": [], "format": "percent", "metricName": "add_payment_rate", "quota": "", "relation": "and", "type": "normal"}, {"analysisParams": "", "eventName": "Charge", "eventNameDisplay": "新增ARPPU", "eventUuid": "KNrKrsa9Xu1uxTp1", "filts": [], "format": "float", "metricName": "new_arppu", "quota": "", "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "LoginRole", "eventNameDisplay": "小 R 人数", "eventUuid": "ndiE-pjGWfnuiDqP", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["小R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "LoginRole", "eventNameDisplay": "中 R 人数", "eventUuid": "i3BzTUoKmAC_cJTH", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["中R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "LoginRole", "eventNameDisplay": "大 R 人数", "eventUuid": "62IMWV5v6xx011WH", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["大R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "LoginRole", "eventNameDisplay": "超 R 人数", "eventUuid": "KZP29UU8SZ_b0Lks", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["超R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "小 R 充值", "eventUuid": "nHuZqlaqgAdmoI9M", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["小R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}], "quota": "money", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "中 R 充值", "eventUuid": "gZ85c9lF5sBxcdba", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["中R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}], "quota": "money", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "大 R 充值", "eventUuid": "6wVnZ6KX-CJ4kL2G", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["大R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}], "quota": "money", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "超 R 充值", "eventUuid": "S7PDdnK4KZjuZ1-f", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241211_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["超R"], "specifiedClusterDate": "2025-04-17", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-25", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-25", "subTableType": "", "tableType": "event", "timeUnit": ""}], "quota": "money", "relation": "and", "type": "normal"}], "projectId": 2}