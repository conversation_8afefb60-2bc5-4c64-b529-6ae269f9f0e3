{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-21 23:59:59", "filts": [{"columnDesc": "内部用户", "columnName": "is_tester", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2024-11-15", "tableType": "user", "timeUnit": ""}, {"columnDesc": "平台名称", "columnName": "platform_id@platform_name", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["联运服", "微信服", "华为服", "3K服"], "specifiedClusterDate": "2024-11-15", "subTableType": "vprop_dict", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2024-11-15", "tableType": "event", "timeUnit": ""}, {"columnDesc": "创角时间（reg_time）", "columnName": "#vp@create_time", "comparator": "relativeCurrentBetween", "filterType": "SIMPLE", "ftv": ["-7", "-1"], "specifiedClusterDate": "2025-04-17", "subTableType": "vprop_sql", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2024-11-15", "tableType": "event", "timeUnit": ""}], "firstDayOfWeek": 5, "groupBy": [{"columnDesc": "角色ID", "columnName": "role_id", "propertyRange": "", "tableType": "event"}, {"columnDesc": "角色名", "columnName": "role_name", "propertyRange": "", "tableType": "user"}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}, {"columnDesc": "创角时间", "columnName": "#vp@create_time_timestamp", "propertyRange": "", "subTableType": "vprop_sql", "tableType": "user", "timeTypeColumnFormart": "day"}, {"columnDesc": "最后登录时间", "columnName": "#vp@last_login_time_format", "propertyRange": "", "subTableType": "vprop_sql", "tableType": "user", "timeTypeColumnFormart": "day"}, {"columnDesc": "累计充值金额", "columnName": "total_recharge_amount", "propertyRange": "", "propertyRangeType": "discrete", "tableType": "user"}], "recentDay": "1-7", "relation": "and", "startTime": "2025-04-15 00:00:00", "timeParticleSize": "day"}, "events": [{"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "充值日志.价格总和", "eventUuid": "WPkJDzx-", "filts": [], "quota": "money", "relation": "and", "type": "normal"}], "projectId": 2}