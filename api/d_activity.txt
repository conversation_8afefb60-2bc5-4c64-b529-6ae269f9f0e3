{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-20 23:59:59", "filts": [{"columnDesc": "内部用户", "columnName": "is_tester", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2024-11-15", "tableType": "user", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-04-21", "subTableType": "", "tableType": "event", "timeUnit": ""}], "firstDayOfWeek": 5, "groupBy": [{"columnDesc": "活动主题分类", "columnName": "#vp@cultivate_id@cultivate_type", "propertyRange": "", "subTableType": "vprop_dict_v", "tableType": "event"}, {"columnDesc": "创角渠道名称", "columnName": "create_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-17 00:00:00", "timeParticleSize": "week"}, "events": [{"analysis": "SUM", "analysisParams": "", "eventName": "Charge", "eventNameDisplay": "充值日志.价格总和", "eventUuid": "WPkJDzx-", "filts": [], "quota": "money", "relation": "and", "type": "normal"}], "projectId": 2}