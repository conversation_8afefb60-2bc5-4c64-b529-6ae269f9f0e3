{"eventView": {"comparedByTime": false, "comparedTimeList": [], "endTime": "2025-04-17 23:59:59", "filts": [{"columnDesc": "是否内部用户", "columnName": "is_tester", "comparator": "isNull", "filterType": "SIMPLE", "ftv": [], "specifiedClusterDate": "2025-03-26", "tableType": "user", "timeUnit": ""}, {"columnDesc": "创角渠道名称", "columnName": "cre_package_id@channel", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["微信小游戏", "苹果", "安卓"], "specifiedClusterDate": "2025-03-26", "subTableType": "vprop_dict", "tableType": "user", "timeUnit": ""}], "firstDayOfWeek": 4, "groupBy": [{"columnDesc": "创角渠道名称", "columnName": "cre_package_id@channel", "propertyRange": "", "subTableType": "vprop_dict", "tableType": "user"}], "recentDay": "1-35", "relation": "and", "startTime": "2025-03-14 00:00:00", "timeParticleSize": "week"}, "events": [{"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_create", "eventNameDisplay": "新增", "eventUuid": "6uDOlXGAWPMBdt6c", "filts": [], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "流水", "eventUuid": "gbKMq5eHra-wwtx4", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "quota": "price", "relation": "and", "type": "normal"}, {"customEvent": "role_pay.price.PER_CAPITA_NUM", "customFilters": [{"filts": [{"filterType": "COMPOUND", "filts": [{"columnDesc": "订单号", "columnName": "order_id", "comparator": "notequal", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "relation": "and"}], "index": 0, "relation": "and"}], "eventName": "付费 ARPU", "eventNameDisplay": "", "eventUuid": "94jgVZrYcDRqcAFO", "filts": [], "format": "float", "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "quotaTimeRanges": [], "relation": "and", "type": "customized"}, {"analysisParams": "", "eventName": "role_create,role_pay", "eventNameDisplay": "新增付费率", "eventUuid": "7_G1BDBxlfpkF2oG", "filts": [], "format": "percent", "metricName": "add_payment_rate", "quota": "", "relation": "and", "type": "normal"}, {"analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "新增ARPPU", "eventUuid": "BhjQJoM1no1mmYOu", "filts": [], "format": "float", "metricName": "new_arppu", "quota": "", "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_login", "eventNameDisplay": "小 R 人数", "eventUuid": "78y5MP_ZFVNEhTvT", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["小R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_login", "eventNameDisplay": "中 R 人数", "eventUuid": "xu9SiRwTzFoltyMF", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["中R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_login", "eventNameDisplay": "大 R 人数", "eventUuid": "SCH7k0r4gR3LcnZg", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["大R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "TRIG_USER_NUM", "analysisParams": "", "eventName": "role_login", "eventNameDisplay": "超 R 人数", "eventUuid": "34pM2pd8BHsAjlsj", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["超R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}], "quota": "", "quotaEntities": [{"index": 0, "taIdMeasure": {"columnDesc": "用户唯一ID", "columnName": "#user_id", "tableType": "event"}}], "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "小 R 充值", "eventUuid": "DKe2BVTJWtG26L89", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["小R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "quota": "price", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "中 R 充值", "eventUuid": "1li6H48f6HcWmL7I", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["中R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "quota": "price", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "大 R 充值", "eventUuid": "u6BA99b5SV80HmgK", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["大R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "quota": "price", "relation": "and", "type": "normal"}, {"analysis": "SUM", "analysisParams": "", "eventName": "role_pay", "eventNameDisplay": "超 R 充值", "eventUuid": "rY7QJ_qSE714z6z6", "filts": [{"columnDesc": "付费层级", "columnName": "tag_20241218_1", "comparator": "equal", "filterType": "SIMPLE", "ftv": ["超R"], "specifiedClusterDate": "2025-03-27", "subTableType": "tag_by_dynamic_condition", "tableType": "user_cluster", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notEqual", "filterType": "SIMPLE", "ftv": ["cheat"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}, {"columnDesc": "订单号", "columnName": "order_id", "comparator": "notInclude", "filterType": "SIMPLE", "ftv": ["d"], "specifiedClusterDate": "2025-03-26", "tableType": "event", "timeUnit": ""}], "quota": "price", "relation": "and", "type": "normal"}], "projectId": 24}