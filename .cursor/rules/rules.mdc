---
description: 
globs: 
alwaysApply: true
---
---
description: 渠道数据监控系统项目开发规则
globs: 
alwaysApply: true
---

# 渠道数据监控系统项目规则

## Git 配置规则
- 使用中文提交信息
- 严格遵循.gitignore文件，排除临时文件和敏感数据
- 推送前必须进行本地测试
- 使用语义化的提交信息格式

## 代码开发规则
- Python代码必须符合PEP8规范
- 函数和类必须有完整的文档字符串
- 关键步骤添加日志记录，避免过度打印
- 配置信息统一在config.py中管理

## 文件管理规则
- 数据文件存放在data/目录
- API相关文件存放在api/目录
- AI分析结果存放在aiapi/目录
- 日志文件存放在logs/目录

## 测试规则
- 编写单元测试脚本
- 测试完成后删除临时测试文件
- 保证代码覆盖率达到80%以上

## 文档规则
- 保持README.md文件更新
- 重要功能变更需要更新架构设计文档
- 不需要为每个任务创建md文档

## 安全规则
- API密钥和敏感信息不得提交到仓库
- 使用环境变量或配置文件管理敏感数据
- 定期检查代码中的硬编码敏感信息
