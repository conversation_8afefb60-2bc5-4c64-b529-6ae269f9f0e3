# 渠道数据监控系统 v2.1.1

## 📊 项目简介

渠道数据监控系统是一个专业的SLG游戏数据分析工具，支持多渠道、多版本的游戏运营数据监控与深度分析。系统集成了数据获取、处理、可视化和AI智能分析功能，为游戏运营团队提供全面的数据洞察。

## ✨ 核心功能

### 🔄 数据获取
- **多版本支持**: 同时支持D版本和E版本数据获取
- **灵活渠道选择**: 支持单渠道、多渠道和全渠道分析
- **自动化调度**: 支持定时数据获取和更新
- **并发处理**: 多线程并发获取提高效率

### 📈 数据分析
- **核心指标监控**: DAU、流水、ARPU、付费率、新增用户等
- **用户分层分析**: 小R、中R、大R、超R四层付费用户分析
- **充值分布分析**: 完整的用户充值金额区间分布
- **活动效果评估**: 各类活动的充值贡献分析
- **趋势对比**: 周度、月度数据趋势对比

### 🤖 AI智能分析
- **多模型支持**: 支持Claude、GPT、Gemini等主流AI模型
- **深度洞察**: 基于8个维度的数据进行智能分析
- **异常检测**: 自动识别数据异常和业务风险
- **策略建议**: 提供具体的运营优化建议

### 📊 可视化报告
- **Excel报告**: 自动生成格式化的Excel分析报告
- **图表嵌入**: 将可视化图表直接嵌入报告中
- **跨平台兼容**: 支持Windows和macOS的中文字体显示
- **自定义模板**: 支持报告模板的灵活配置

### 📱 企业微信推送
- **纯文件推送**: 报告生成完成后直接推送Excel文件到企业微信群聊
- **无干扰设计**: 仅推送文件，不发送任何文字通知，保持群聊简洁
- **一键获取**: 团队成员直接在群聊中下载最新报告文件
- **灵活配置**: 支持启用/禁用文件推送功能
- **错误处理**: 完善的异常处理和推送失败提示

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 操作系统: Windows 10+ / macOS 10.14+

### 安装依赖
```bash
# 运行依赖安装脚本
./2、安装依赖库.bat  # Windows
# 或手动安装
pip install pandas numpy matplotlib seaborn requests xlsxwriter openai
```

## 📱 企业微信推送配置

### 设置企业微信机器人
1. **创建机器人**
   - 在企业微信群聊中点击右上角设置
   - 选择"群机器人" → "添加机器人"
   - 设置机器人名称（如：数据分析助手）
   - 复制生成的Webhook地址

2. **配置推送参数**
   ```python
   # 在 config.py 中设置
   WECHAT_CONFIG = {
       "ENABLED": True,  # 启用推送
       "WEBHOOK_URL": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=你的KEY",
       "PUSH_ON_SUCCESS": True,  # 成功时推送
       "PUSH_ON_ERROR": True,    # 失败时推送
       "SEND_FILE": True,        # 直接推送文件（True=推送文件，False=仅发通知）
   }
   ```

3. **测试推送功能**
   ```bash
   python test_wechat_push.py
   ```

### 推送功能说明
- 📎 **纯文件推送**: 仅推送Excel报告文件到群聊，无任何文字消息
- 🚀 **简洁高效**: 无通知消息，直接获取文件，减少群聊干扰
- ✅ **成功推送**: 分析成功时直接推送文件到群聊
- ❌ **失败静默**: 分析失败时不进行任何推送，保持群聊清洁
- 🤖 **兼容性**: 保持API接口兼容，支持灵活配置

## 🚀 运行系统

### 基本使用
```bash
# 运行完整分析（会自动推送企业微信通知）
python main.py

# 或使用批处理脚本
./3、运行.bat

