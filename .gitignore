# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/*.log

# 临时文件
*.tmp
*.temp

# AI分析结果文件
aiapi/

# 数据库文件
*.db
*.sqlite3

# 配置文件（如果包含敏感信息）
config_local.py
.env.local

# Excel临时文件
~$*.xlsx
~$*.xls
~*.csv
~*.xlsx
*.xlsx

# API密钥和敏感数据
api_keys.txt
secrets.txt 