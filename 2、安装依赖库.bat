@echo off
chcp 65001 >nul
echo ========================================
echo 开始安装Python依赖包(使用国内镜像源)
echo ========================================

echo 正在升级pip...
pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple

echo 正在安装requests...
pip install requests -i https://pypi.tuna.tsinghua.edu.cn/simple

echo 正在安装numpy...
pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple --prefer-binary

echo 正在安装pandas...
pip install pandas -i https://pypi.tuna.tsinghua.edu.cn/simple --only-binary=:all:

echo 正在安装matplotlib...
pip install matplotlib -i https://pypi.tuna.tsinghua.edu.cn/simple

echo 正在安装seaborn...
pip install seaborn -i https://pypi.tuna.tsinghua.edu.cn/simple

echo 正在安装openai...
pip install openai -i https://pypi.tuna.tsinghua.edu.cn/simple

echo 正在安装xlsxwriter...
pip install xlsxwriter -i https://pypi.tuna.tsinghua.edu.cn/simple

echo.
echo ========================================
echo 依赖包安装完成！
echo ========================================
pause

python3 -c "import requests, numpy, pandas, matplotlib, seaborn, openai, xlsxwriter; print('所有包都可以正常使用！')"